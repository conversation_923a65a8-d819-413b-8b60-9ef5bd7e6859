using System;
using System.Threading;
using static Kiva_MIDI.RaylibPInvoke;

namespace Kiva_MIDI
{
    /// <summary>
    /// Test to validate the dual-player system implementation
    /// </summary>
    public class TestDualPlayer
    {
        public static void TestDualPlayerSystem()
        {
            Console.WriteLine("Testing Dual-Player System...");
            
            try
            {
                // Create settings
                var settings = new Settings();
                
                // Create playing state
                var playingState = new PlayingState();
                
                // Test MIDITexturePreRenderPlayer creation
                Console.WriteLine("1. Testing MIDITexturePreRenderPlayer creation...");
                var texturePlayer = new MIDITexturePreRenderPlayer(settings);
                Console.WriteLine("   ✓ MIDITexturePreRenderPlayer created successfully");
                
                // Test setting time
                Console.WriteLine("2. Testing time assignment...");
                texturePlayer.Time = playingState;
                Console.WriteLine("   ✓ Time assigned successfully");
                
                // Test note speed changes
                Console.WriteLine("3. Testing note speed changes...");
                texturePlayer.NoteSpeed = 2.0;
                if (Math.Abs(texturePlayer.NoteSpeed - 2.0) < 0.001)
                {
                    Console.WriteLine("   ✓ Note speed change successful");
                }
                else
                {
                    Console.WriteLine("   ✗ Note speed change failed");
                }
                
                // Test texture properties
                Console.WriteLine("4. Testing texture properties...");
                var texture = texturePlayer.GetTexture();
                var startTime = texturePlayer.GetTextureStartTime();
                var timeRange = texturePlayer.GetTextureTimeRange();
                Console.WriteLine($"   ✓ Texture ID: {texture.id}");
                Console.WriteLine($"   ✓ Start Time: {startTime}");
                Console.WriteLine($"   ✓ Time Range: {timeRange}");
                
                // Test RaylibMIDIRenderer with texture player
                Console.WriteLine("5. Testing RaylibMIDIRenderer integration...");
                
                // Initialize raylib for testing
                Raylib.InitWindow(800, 600, "Dual Player Test");
                
                var renderer = new RaylibRenderer(settings);
                renderer.SetScreenSize(800, 600);
                
                var midiRenderer = new RaylibMIDIRenderer(renderer, settings);
                midiRenderer.Time = playingState;
                
                Console.WriteLine("   ✓ RaylibMIDIRenderer created with texture player");
                
                // Test note speed synchronization
                Console.WriteLine("6. Testing note speed synchronization...");
                midiRenderer.SetNoteSpeed(1.5);
                Console.WriteLine("   ✓ Note speed synchronized between players");
                
                // Test seek simulation
                Console.WriteLine("7. Testing seek simulation...");
                playingState.Navigate(10.0); // Seek to 10 seconds
                Thread.Sleep(100); // Give time for async operations
                Console.WriteLine("   ✓ Seek operation completed");
                
                // Test speed change simulation
                Console.WriteLine("8. Testing speed change simulation...");
                playingState.ChangeSpeed(2.0); // Change playback speed
                Thread.Sleep(100); // Give time for async operations
                Console.WriteLine("   ✓ Speed change operation completed");
                
                // Cleanup
                Console.WriteLine("9. Testing cleanup...");
                midiRenderer.Dispose();
                texturePlayer.Dispose();
                renderer.Dispose();
                Raylib.CloseWindow();
                Console.WriteLine("   ✓ Cleanup completed successfully");
                
                Console.WriteLine("\n✓ All dual-player system tests passed!");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ Dual-player system test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        public static void TestTextureRendering()
        {
            Console.WriteLine("\nTesting Texture Rendering...");
            
            try
            {
                // Test texture creation and basic operations
                Console.WriteLine("1. Testing texture creation...");
                
                Raylib.InitWindow(800, 600, "Texture Test");
                
                var renderTexture = Raylib.LoadRenderTexture(1920, 4096);
                Console.WriteLine($"   ✓ Render texture created: ID={renderTexture.id}, Size={renderTexture.texture.width}x{renderTexture.texture.height}");
                
                // Test texture rendering
                Console.WriteLine("2. Testing texture rendering...");
                Raylib.BeginTextureMode(renderTexture);
                Raylib.ClearBackground(Color.BLACK);
                
                // Draw some test rectangles
                Raylib.DrawRectangle(100, 100, 200, 50, Color.RED);
                Raylib.DrawRectangle(400, 200, 150, 75, Color.GREEN);
                Raylib.DrawRectangle(700, 300, 100, 100, Color.BLUE);
                
                Raylib.EndTextureMode();
                Console.WriteLine("   ✓ Texture rendering completed");
                
                // Test texture drawing
                Console.WriteLine("3. Testing texture drawing...");
                
                for (int frame = 0; frame < 60 && !Raylib.WindowShouldClose(); frame++)
                {
                    Raylib.BeginDrawing();
                    Raylib.ClearBackground(Color.DARKGRAY);
                    
                    // Draw part of the texture
                    var sourceRect = new Rectangle(0, frame * 10, 800, 600);
                    var destRect = new Rectangle(0, 0, 800, 600);
                    
                    Raylib.DrawTexturePro(renderTexture.texture, sourceRect, destRect, new Vector2(0, 0), 0.0f, Color.WHITE);
                    
                    Raylib.DrawText($"Frame: {frame}", 10, 10, 20, Color.WHITE);
                    Raylib.DrawText("Testing texture scrolling", 10, 40, 16, Color.WHITE);
                    
                    Raylib.EndDrawing();
                    
                    Thread.Sleep(16); // ~60 FPS
                }
                
                Console.WriteLine("   ✓ Texture drawing test completed");
                
                // Cleanup
                Raylib.UnloadRenderTexture(renderTexture);
                Raylib.CloseWindow();
                
                Console.WriteLine("\n✓ All texture rendering tests passed!");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ Texture rendering test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        public static void Main(string[] args)
        {
            Console.WriteLine("=== Dual-Player System Validation ===\n");
            
            TestDualPlayerSystem();
            TestTextureRendering();
            
            Console.WriteLine("\n=== Test Suite Complete ===");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
