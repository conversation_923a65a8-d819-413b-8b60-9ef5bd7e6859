using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Second Player that renders MIDI notes to a texture buffer as fast as possible.
    /// Only responsible for drawing notes to texture - no audio or other behaviors.
    /// Restarts buffer on Main Player seeks and recalculates on note speed changes.
    /// </summary>
    public class MIDITexturePreRenderPlayer : IDisposable
    {
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private bool disposed = false;
        
        // Texture rendering properties
        private RenderTexture2D renderTexture;
        private int textureWidth;
        private int textureHeight;
        private double textureTimeRange; // How many seconds of MIDI the texture represents
        private double textureStartTime; // The MIDI time at the top of the texture
        private double noteSpeed = 1.0; // Current note speed multiplier
        
        // Rendering thread management
        private Task renderingThread;
        private CancellationTokenSource cancelToken;
        private readonly object textureLock = new object();
        private volatile bool needsRerender = false;
        
        // Note rendering constants
        private const int DEFAULT_TEXTURE_WIDTH = 1920;
        private const int DEFAULT_TEXTURE_HEIGHT = 4096; // Large height for smooth scrolling
        private const double DEFAULT_TIME_RANGE = 30.0; // 30 seconds of MIDI data
        
        // Piano key layout (matching existing system)
        private static readonly bool[] blackKeys = new bool[256];
        private double[] x1array = new double[256];
        private double[] wdtharray = new double[256];
        
        static MIDITexturePreRenderPlayer()
        {
            // Initialize black keys pattern (same as existing system)
            for (int i = 0; i < 256; i++)
            {
                int note = i % 12;
                blackKeys[i] = note == 1 || note == 3 || note == 6 || note == 8 || note == 10;
            }
        }
        
        public MIDIFile File
        {
            get => file;
            set
            {
                lock (textureLock)
                {
                    file = value;
                    if (file != null)
                    {
                        InitializeKeyLayout();
                        StartRender(true);
                    }
                    else
                    {
                        StopRender();
                    }
                }
            }
        }
        
        public PlayingState Time
        {
            get => time;
            set
            {
                if (time != null)
                {
                    time.TimeChanged -= OnTimeChanged;
                    time.SpeedChanged -= OnSpeedChanged;
                }
                time = value;
                if (time != null)
                {
                    time.TimeChanged += OnTimeChanged;
                    time.SpeedChanged += OnSpeedChanged;
                }
            }
        }
        
        public double NoteSpeed
        {
            get => noteSpeed;
            set
            {
                if (Math.Abs(noteSpeed - value) > 0.001)
                {
                    noteSpeed = value;
                    OnNoteSpeedChanged();
                }
            }
        }
        
        public RenderTexture2D GetTexture()
        {
            lock (textureLock)
            {
                return renderTexture;
            }
        }
        
        public double GetTextureStartTime()
        {
            return textureStartTime;
        }
        
        public double GetTextureTimeRange()
        {
            return textureTimeRange;
        }
        
        public MIDITexturePreRenderPlayer(Settings settings, int textureWidth = DEFAULT_TEXTURE_WIDTH, int textureHeight = DEFAULT_TEXTURE_HEIGHT)
        {
            this.settings = settings;
            this.textureWidth = textureWidth;
            this.textureHeight = textureHeight;
            this.textureTimeRange = DEFAULT_TIME_RANGE;
            
            InitializeTexture();
        }
        
        private void InitializeTexture()
        {
            lock (textureLock)
            {
                if (renderTexture.id != 0)
                {
                    Raylib.UnloadRenderTexture(renderTexture);
                }
                
                renderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
                
                // Clear texture to black
                Raylib.BeginTextureMode(renderTexture);
                Raylib.ClearBackground(new RaylibColor(0, 0, 0, 255));
                Raylib.EndTextureMode();
            }
        }
        
        private void InitializeKeyLayout()
        {
            // Initialize piano key layout (same logic as existing renderer)
            int firstNote = 0;
            int lastNote = 128;
            
            if (settings?.General?.KeyRange == KeyRangeTypes.Key88)
            {
                firstNote = 21;
                lastNote = 109;
            }
            else if (settings?.General?.KeyRange == KeyRangeTypes.Key256)
            {
                firstNote = 0;
                lastNote = 256;
            }
            
            // Calculate key positions and widths
            double whiteKeyWidth = (double)textureWidth / GetWhiteKeyCount(firstNote, lastNote);
            double blackKeyWidth = whiteKeyWidth * 0.6;
            
            double currentX = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i]) // White key
                {
                    x1array[i] = currentX;
                    wdtharray[i] = whiteKeyWidth;
                    currentX += whiteKeyWidth;
                }
                else // Black key
                {
                    x1array[i] = currentX - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
            }
        }
        
        private int GetWhiteKeyCount(int firstNote, int lastNote)
        {
            int count = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i]) count++;
            }
            return count;
        }
        
        private void OnTimeChanged()
        {
            // Main Player seeked - restart texture rendering from new position
            StartRender(false);
        }
        
        private void OnSpeedChanged()
        {
            // Main Player speed changed - restart texture rendering
            StartRender(true);
        }
        
        private void OnNoteSpeedChanged()
        {
            // Note speed changed - recalculate and re-render texture
            StartRender(true);
        }
        
        private void StartRender(bool forceRestart)
        {
            if (file == null || time == null) return;
            
            double currentTime = time.GetTime();
            
            if (!forceRestart)
            {
                // Check if current time is still within rendered texture range
                double textureEndTime = textureStartTime + textureTimeRange;
                if (currentTime >= textureStartTime && currentTime <= textureEndTime - textureTimeRange * 0.1)
                {
                    return; // No need to re-render yet
                }
            }
            
            // Stop current rendering
            StopRender();
            
            // Start new rendering from current time
            textureStartTime = currentTime;
            needsRerender = true;
            
            cancelToken = new CancellationTokenSource();
            renderingThread = Task.Run(() => RenderTextureAsync(cancelToken.Token));
        }
        
        private void StopRender()
        {
            if (cancelToken != null)
            {
                cancelToken.Cancel();
                cancelToken = null;
            }
            
            if (renderingThread != null)
            {
                try
                {
                    renderingThread.Wait(1000); // Wait up to 1 second
                }
                catch (AggregateException)
                {
                    // Ignore cancellation exceptions
                }
                renderingThread = null;
            }
        }
        
        private async Task RenderTextureAsync(CancellationToken cancellationToken)
        {
            try
            {
                if (file == null || !(file is MIDIMemoryFile memoryFile))
                    return;

                lock (textureLock)
                {
                    // Clear texture
                    Raylib.BeginTextureMode(renderTexture);
                    Raylib.ClearBackground(new RaylibColor(0, 0, 0, 255));
                    Raylib.EndTextureMode();
                }

                // Render notes as fast as possible
                await Task.Run(() => RenderNotesToTexture(memoryFile, cancellationToken), cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in texture rendering: {ex.Message}");
            }
        }

        private void RenderNotesToTexture(MIDIMemoryFile memoryFile, CancellationToken cancellationToken)
        {
            double endTime = textureStartTime + textureTimeRange;

            // Get note colors (same as existing system)
            var colors = memoryFile.MidiNoteColors;

            lock (textureLock)
            {
                Raylib.BeginTextureMode(renderTexture);

                try
                {
                    // Render white key notes first (background layer)
                    for (int k = 0; k < 256; k++)
                    {
                        if (cancellationToken.IsCancellationRequested) return;
                        if (blackKeys[k]) continue; // Skip black keys in this pass
                        RenderNotesForKey(memoryFile, k, colors, cancellationToken);
                    }

                    // Render black key notes second (foreground layer)
                    for (int k = 0; k < 256; k++)
                    {
                        if (cancellationToken.IsCancellationRequested) return;
                        if (!blackKeys[k]) continue; // Skip white keys in this pass
                        RenderNotesForKey(memoryFile, k, colors, cancellationToken);
                    }
                }
                finally
                {
                    Raylib.EndTextureMode();
                }
            }
        }

        private void RenderNotesForKey(MIDIMemoryFile memoryFile, int keyIndex, NoteCol[] colors, CancellationToken cancellationToken)
        {
            if (keyIndex >= memoryFile.Notes.Length) return;

            var notes = memoryFile.Notes[keyIndex];
            if (notes.Length == 0) return;

            double endTime = textureStartTime + textureTimeRange;
            float keyLeft = (float)x1array[keyIndex];
            float keyRight = keyLeft + (float)wdtharray[keyIndex];

            foreach (var note in notes)
            {
                if (cancellationToken.IsCancellationRequested) return;

                // Skip notes outside our time range
                if (note.end < textureStartTime || note.start > endTime)
                    continue;

                // Calculate note position in texture coordinates
                double noteStartTime = Math.Max(note.start, textureStartTime);
                double noteEndTime = Math.Min(note.end, endTime);

                // Convert time to texture Y coordinates (flipped - notes move upward)
                // Top of texture (Y=0) represents textureStartTime
                // Bottom of texture (Y=textureHeight) represents textureStartTime + textureTimeRange
                float noteTop = (float)((noteStartTime - textureStartTime) / textureTimeRange * textureHeight);
                float noteBottom = (float)((noteEndTime - textureStartTime) / textureTimeRange * textureHeight);

                // Apply note speed scaling
                float speedScaledTop = noteTop / (float)noteSpeed;
                float speedScaledBottom = noteBottom / (float)noteSpeed;

                // Ensure note is visible
                if (speedScaledBottom <= speedScaledTop || speedScaledTop >= textureHeight || speedScaledBottom <= 0)
                    continue;

                // Get note color
                var noteColor = colors[note.colorPointer];
                var raylibColor = new RaylibColor(
                    (byte)((noteColor.rgba >> 24) & 0xFF),
                    (byte)((noteColor.rgba >> 16) & 0xFF),
                    (byte)((noteColor.rgba >> 8) & 0xFF),
                    (byte)(noteColor.rgba & 0xFF)
                );

                // Draw note rectangle with gradient effect (same style as existing renderer)
                DrawGradientNote(keyLeft, speedScaledTop, keyRight - keyLeft, speedScaledBottom - speedScaledTop, raylibColor);
            }
        }

        private void DrawGradientNote(float x, float y, float width, float height, RaylibColor color)
        {
            // Create gradient effect similar to existing renderer
            var darkerColor = new RaylibColor(
                (byte)(color.r * 0.7f),
                (byte)(color.g * 0.7f),
                (byte)(color.b * 0.7f),
                color.a
            );

            // Draw main note body
            Raylib.DrawRectangle((int)x, (int)y, (int)width, (int)height, color);

            // Add darker borders for depth
            if (width > 2 && height > 2)
            {
                Raylib.DrawRectangleLines((int)x, (int)y, (int)width, (int)height, darkerColor);
            }
        }

        public void Dispose()
        {
            if (disposed) return;
            disposed = true;

            StopRender();

            if (time != null)
            {
                time.TimeChanged -= OnTimeChanged;
                time.SpeedChanged -= OnSpeedChanged;
            }

            lock (textureLock)
            {
                if (renderTexture.id != 0)
                {
                    Raylib.UnloadRenderTexture(renderTexture);
                    renderTexture = new RenderTexture2D();
                }
            }
        }
    }
}
