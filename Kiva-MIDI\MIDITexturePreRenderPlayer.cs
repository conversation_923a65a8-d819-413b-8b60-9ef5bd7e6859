using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using static Kiva_MIDI.RaylibPInvoke;

namespace Kiva_MIDI
{
    /// <summary>
    /// A specialized player that pre-renders MIDI notes to a texture buffer
    /// This player runs independently and as fast as possible, only drawing notes to texture
    /// </summary>
    public class MIDITexturePreRenderPlayer : IDisposable
    {
        private Settings settings;
        private MIDIFile file = null;
        private PlayingState mainPlayerTime;
        private PlayingState internalTime;
        private bool disposed = false;
        private Task renderThread;
        private CancellationTokenSource cancellationToken;
        
        // Texture rendering properties
        private RenderTexture2D preRenderTexture;
        private int textureWidth = 4096;  // Large texture width for high quality
        private int textureHeight = 2048; // Large texture height for full MIDI range
        private bool textureNeedsUpdate = true;
        private double currentNoteSpeed = 1.0;
        private double renderStartTime = 0.0;
        private double renderEndTime = 0.0;
        
        // Rendering state
        private readonly object renderLock = new object();
        private bool isRendering = false;
        
        public MIDIFile File
        {
            get => file;
            set
            {
                lock (renderLock)
                {
                    file = value;
                    if (file != null)
                    {
                        RestartPreRendering();
                    }
                }
            }
        }
        
        public PlayingState MainPlayerTime
        {
            get => mainPlayerTime;
            set
            {
                if (mainPlayerTime != null)
                {
                    mainPlayerTime.TimeChanged -= OnMainPlayerTimeChanged;
                    mainPlayerTime.SpeedChanged -= OnMainPlayerSpeedChanged;
                }
                
                mainPlayerTime = value;
                
                if (mainPlayerTime != null)
                {
                    mainPlayerTime.TimeChanged += OnMainPlayerTimeChanged;
                    mainPlayerTime.SpeedChanged += OnMainPlayerSpeedChanged;
                }
            }
        }
        
        public RenderTexture2D PreRenderedTexture => preRenderTexture;
        public bool IsRendering => isRendering;
        public double RenderProgress { get; private set; } = 0.0;
        
        public MIDITexturePreRenderPlayer(Settings settings)
        {
            this.settings = settings;
            internalTime = new PlayingState();
            
            // Initialize texture
            InitializeTexture();
            
            // Start render thread
            cancellationToken = new CancellationTokenSource();
            renderThread = Task.Run(RenderThreadLoop, cancellationToken.Token);
        }
        
        private void InitializeTexture()
        {
            // Create a large render texture for pre-rendering notes
            preRenderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
            
            // Clear the texture initially
            Raylib.BeginTextureMode(preRenderTexture);
            Raylib.ClearBackground(new Color(0, 0, 0, 0)); // Transparent background
            Raylib.EndTextureMode();
        }
        
        private void OnMainPlayerTimeChanged()
        {
            // When main player seeks, restart pre-rendering from new position
            RestartPreRendering();
        }
        
        private void OnMainPlayerSpeedChanged()
        {
            // When note speed changes, restart pre-rendering with new speed
            if (settings?.Volatile?.Size != currentNoteSpeed)
            {
                currentNoteSpeed = settings?.Volatile?.Size ?? 1.0;
                RestartPreRendering();
            }
        }
        
        private void RestartPreRendering()
        {
            lock (renderLock)
            {
                textureNeedsUpdate = true;
                if (mainPlayerTime != null)
                {
                    renderStartTime = mainPlayerTime.GetTime();
                    // Calculate how much to pre-render based on note speed
                    double timeWindow = currentNoteSpeed * 2.0; // Render 2x the visible window
                    renderEndTime = renderStartTime + timeWindow;
                    
                    if (file != null)
                    {
                        renderEndTime = Math.Min(renderEndTime, file.MidiLength);
                    }
                }
            }
        }
        
        private async Task RenderThreadLoop()
        {
            while (!disposed && !cancellationToken.Token.IsCancellationRequested)
            {
                try
                {
                    if (textureNeedsUpdate && file != null)
                    {
                        await PreRenderToTexture();
                    }
                    
                    // Sleep briefly to avoid excessive CPU usage
                    await Task.Delay(16, cancellationToken.Token); // ~60 FPS update rate
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in texture pre-render thread: {ex.Message}");
                    await Task.Delay(100, cancellationToken.Token);
                }
            }
        }
        
        private async Task PreRenderToTexture()
        {
            if (file == null || !(file is MIDIMemoryFile memoryFile))
                return;
                
            lock (renderLock)
            {
                if (isRendering) return;
                isRendering = true;
            }
            
            try
            {
                // Begin rendering to texture
                Raylib.BeginTextureMode(preRenderTexture);
                Raylib.ClearBackground(new Color(0, 0, 0, 0)); // Clear with transparency
                
                // Set up internal time for fast rendering
                internalTime.Navigate(renderStartTime);
                internalTime.ChangeSpeed(100.0); // Render at 100x speed for fast processing
                
                double currentRenderTime = renderStartTime;
                double timeStep = 0.001; // Small time steps for smooth rendering
                
                while (currentRenderTime < renderEndTime && !cancellationToken.Token.IsCancellationRequested)
                {
                    // Update progress
                    RenderProgress = (currentRenderTime - renderStartTime) / (renderEndTime - renderStartTime);
                    
                    // Render notes at this time position
                    RenderNotesAtTime(memoryFile, currentRenderTime);
                    
                    currentRenderTime += timeStep;
                    
                    // Yield control occasionally to prevent blocking
                    if ((int)(currentRenderTime * 1000) % 10 == 0)
                    {
                        await Task.Yield();
                    }
                }
                
                Raylib.EndTextureMode();
                
                textureNeedsUpdate = false;
                RenderProgress = 1.0;
            }
            finally
            {
                lock (renderLock)
                {
                    isRendering = false;
                }
            }
        }
        
        private void RenderNotesAtTime(MIDIMemoryFile file, double currentTime)
        {
            // This method renders notes to the texture at a specific time
            // Similar to the main renderer but optimized for texture output
            
            try
            {
                file.SetColorEvents(currentTime);
                var colors = file.MidiNoteColors;
                if (colors == null) return;
                
                double timeScale = currentNoteSpeed;
                double renderCutoff = currentTime + timeScale;
                
                // Calculate texture coordinates based on time
                float textureY = (float)((currentTime - renderStartTime) / (renderEndTime - renderStartTime) * textureHeight);
                
                // Render notes for each key
                for (int k = 0; k < 256; k++)
                {
                    if (file.Notes == null || k >= file.Notes.Length)
                        continue;
                        
                    var notes = file.Notes[k];
                    if (notes == null || notes.Length == 0)
                        continue;
                    
                    // Calculate key position in texture
                    float keyX = (float)(k / 256.0 * textureWidth);
                    float keyWidth = textureWidth / 256.0f;
                    
                    // Render notes for this key
                    foreach (var note in notes)
                    {
                        if (note.end < currentTime - 0.1)
                            continue;
                        if (note.start > renderCutoff)
                            break;
                            
                        if (note.start <= renderCutoff && note.end >= currentTime)
                        {
                            // Calculate note dimensions in texture space
                            float noteStart = (float)((note.start - renderStartTime) / (renderEndTime - renderStartTime) * textureHeight);
                            float noteEnd = (float)((note.end - renderStartTime) / (renderEndTime - renderStartTime) * textureHeight);
                            float noteHeight = noteEnd - noteStart;
                            
                            if (noteHeight > 0)
                            {
                                // Get note color
                                var noteColor = colors[note.colorPointer];
                                var raylibColor = new Color(
                                    (byte)((noteColor.rgba >> 24) & 0xFF),
                                    (byte)((noteColor.rgba >> 16) & 0xFF),
                                    (byte)((noteColor.rgba >> 8) & 0xFF),
                                    (byte)(noteColor.rgba & 0xFF)
                                );
                                
                                // Draw note rectangle to texture
                                Raylib.DrawRectangle((int)keyX, (int)noteStart, (int)keyWidth, (int)noteHeight, raylibColor);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering notes at time {currentTime}: {ex.Message}");
            }
        }
        
        public void Dispose()
        {
            if (disposed) return;
            disposed = true;
            
            // Clean up event handlers
            if (mainPlayerTime != null)
            {
                mainPlayerTime.TimeChanged -= OnMainPlayerTimeChanged;
                mainPlayerTime.SpeedChanged -= OnMainPlayerSpeedChanged;
            }
            
            // Cancel and wait for render thread
            cancellationToken?.Cancel();
            try
            {
                renderThread?.Wait(1000); // Wait up to 1 second
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error waiting for render thread to complete: {ex.Message}");
            }
            
            // Clean up texture
            if (preRenderTexture.id != 0)
            {
                Raylib.UnloadRenderTexture(preRenderTexture);
            }
            
            cancellationToken?.Dispose();
        }
    }
}
