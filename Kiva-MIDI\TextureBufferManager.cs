using System;
using System.Collections.Generic;
using static Kiva_MIDI.RaylibPInvoke;

namespace Kiva_MIDI
{
    /// <summary>
    /// Manages texture buffers for pre-rendered MIDI visualization
    /// Handles memory management, texture resizing, and buffer coordination
    /// </summary>
    public class TextureBufferManager : IDisposable
    {
        private struct TextureBuffer
        {
            public RenderTexture2D texture;
            public double startTime;
            public double endTime;
            public bool isValid;
            public DateTime lastUsed;
        }

        private Settings settings;
        private List<TextureBuffer> buffers;
        private int maxBuffers;
        private int textureWidth;
        private int textureHeight;
        private double bufferDurationSeconds;
        private bool disposed = false;
        private readonly object lockObject = new object();

        public int TextureWidth => textureWidth;
        public int TextureHeight => textureHeight;
        public double BufferDurationSeconds => bufferDurationSeconds;
        public int ActiveBufferCount => buffers?.Count ?? 0;

        public TextureBufferManager(Settings settings, int textureWidth, int textureHeight, 
                                   double bufferDurationSeconds = 30.0, int maxBuffers = 10)
        {
            this.settings = settings;
            this.textureWidth = textureWidth;
            this.textureHeight = textureHeight;
            this.bufferDurationSeconds = bufferDurationSeconds;
            this.maxBuffers = maxBuffers;
            this.buffers = new List<TextureBuffer>();
        }

        /// <summary>
        /// Gets or creates a texture buffer for the specified time range
        /// </summary>
        public RenderTexture2D GetOrCreateBuffer(double startTime, double endTime)
        {
            lock (lockObject)
            {
                if (disposed)
                    throw new ObjectDisposedException(nameof(TextureBufferManager));

                // Look for existing buffer that covers this time range
                for (int i = 0; i < buffers.Count; i++)
                {
                    var buffer = buffers[i];
                    if (buffer.isValid && buffer.startTime <= startTime && buffer.endTime >= endTime)
                    {
                        // Update last used time
                        buffer.lastUsed = DateTime.UtcNow;
                        buffers[i] = buffer;
                        return buffer.texture;
                    }
                }

                // Need to create a new buffer
                return CreateNewBuffer(startTime, endTime);
            }
        }

        /// <summary>
        /// Creates a new texture buffer for the specified time range
        /// </summary>
        private RenderTexture2D CreateNewBuffer(double startTime, double endTime)
        {
            // Clean up old buffers if we're at the limit
            if (buffers.Count >= maxBuffers)
            {
                CleanupOldestBuffer();
            }

            // Create new render texture
            var renderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
            
            if (renderTexture.id == 0)
            {
                throw new InvalidOperationException("Failed to create render texture");
            }

            // Add to buffer list
            var newBuffer = new TextureBuffer
            {
                texture = renderTexture,
                startTime = startTime,
                endTime = endTime,
                isValid = true,
                lastUsed = DateTime.UtcNow
            };

            buffers.Add(newBuffer);
            return renderTexture;
        }

        /// <summary>
        /// Invalidates buffers that overlap with the specified time range
        /// Used when seeking or when note speed changes
        /// </summary>
        public void InvalidateBuffersInRange(double startTime, double endTime)
        {
            lock (lockObject)
            {
                if (disposed)
                    return;

                for (int i = buffers.Count - 1; i >= 0; i--)
                {
                    var buffer = buffers[i];
                    
                    // Check if buffer overlaps with the invalidation range
                    if (buffer.isValid && 
                        !(buffer.endTime < startTime || buffer.startTime > endTime))
                    {
                        // Buffer overlaps, invalidate it
                        if (buffer.texture.id != 0)
                        {
                            Raylib.UnloadRenderTexture(buffer.texture);
                        }
                        buffers.RemoveAt(i);
                    }
                }
            }
        }

        /// <summary>
        /// Invalidates all buffers (used when MIDI file changes or major settings change)
        /// </summary>
        public void InvalidateAllBuffers()
        {
            lock (lockObject)
            {
                if (disposed)
                    return;

                foreach (var buffer in buffers)
                {
                    if (buffer.texture.id != 0)
                    {
                        Raylib.UnloadRenderTexture(buffer.texture);
                    }
                }
                buffers.Clear();
            }
        }

        /// <summary>
        /// Removes the oldest buffer to make room for new ones
        /// </summary>
        private void CleanupOldestBuffer()
        {
            if (buffers.Count == 0)
                return;

            int oldestIndex = 0;
            DateTime oldestTime = buffers[0].lastUsed;

            for (int i = 1; i < buffers.Count; i++)
            {
                if (buffers[i].lastUsed < oldestTime)
                {
                    oldestTime = buffers[i].lastUsed;
                    oldestIndex = i;
                }
            }

            var oldestBuffer = buffers[oldestIndex];
            if (oldestBuffer.texture.id != 0)
            {
                Raylib.UnloadRenderTexture(oldestBuffer.texture);
            }
            buffers.RemoveAt(oldestIndex);
        }

        /// <summary>
        /// Resizes all texture buffers (used when window size changes)
        /// </summary>
        public void ResizeBuffers(int newWidth, int newHeight)
        {
            lock (lockObject)
            {
                if (disposed)
                    return;

                if (newWidth == textureWidth && newHeight == textureHeight)
                    return;

                // Invalidate all existing buffers since they're the wrong size
                InvalidateAllBuffers();

                // Update dimensions
                textureWidth = newWidth;
                textureHeight = newHeight;
            }
        }

        /// <summary>
        /// Gets the texture buffer that contains the specified time, if any
        /// </summary>
        public RenderTexture2D? GetBufferForTime(double time)
        {
            lock (lockObject)
            {
                if (disposed)
                    return null;

                foreach (var buffer in buffers)
                {
                    if (buffer.isValid && time >= buffer.startTime && time <= buffer.endTime)
                    {
                        return buffer.texture;
                    }
                }

                return null;
            }
        }

        /// <summary>
        /// Performs cleanup of unused buffers based on age
        /// </summary>
        public void PerformMaintenance()
        {
            lock (lockObject)
            {
                if (disposed)
                    return;

                var cutoffTime = DateTime.UtcNow.AddMinutes(-5); // Remove buffers unused for 5 minutes

                for (int i = buffers.Count - 1; i >= 0; i--)
                {
                    var buffer = buffers[i];
                    if (buffer.lastUsed < cutoffTime)
                    {
                        if (buffer.texture.id != 0)
                        {
                            Raylib.UnloadRenderTexture(buffer.texture);
                        }
                        buffers.RemoveAt(i);
                    }
                }
            }
        }

        public void Dispose()
        {
            if (!disposed)
            {
                lock (lockObject)
                {
                    InvalidateAllBuffers();
                    disposed = true;
                }
            }
        }
    }
}
