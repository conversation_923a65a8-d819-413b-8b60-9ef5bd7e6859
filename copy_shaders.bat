@echo off
echo Copying shader files to output directories...

REM Create shader directories if they don't exist
if not exist "bin\Debug\shaders" mkdir "bin\Debug\shaders"
if not exist "bin\Release\shaders" mkdir "bin\Release\shaders"

REM Copy shader files
copy "Kiva-MIDI\shaders\*.vs" "bin\Debug\shaders\" >nul 2>&1
copy "Kiva-MIDI\shaders\*.fs" "bin\Debug\shaders\" >nul 2>&1
copy "Kiva-MIDI\shaders\*.vs" "bin\Release\shaders\" >nul 2>&1
copy "Kiva-MIDI\shaders\*.fs" "bin\Release\shaders\" >nul 2>&1

echo Shader files copied successfully!
echo GPU-accelerated note rendering is now available.
echo Press 'G' in the application to toggle between GPU and CPU rendering.
