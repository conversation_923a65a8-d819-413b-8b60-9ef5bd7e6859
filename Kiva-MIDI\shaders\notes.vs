#version 330

// Vertex shader for GPU-accelerated note rendering
// Input vertex attributes
in vec3 vertexPosition;
in vec2 vertexTexCoord;
in vec4 vertexColor;

// Uniforms
uniform mat4 mvp;
uniform float screenWidth;
uniform float screenHeight;
uniform float keyboardHeight;

// Output to fragment shader
out vec2 fragTexCoord;
out vec4 fragColor;
out vec2 screenPos;

void main()
{
    // Pass texture coordinates and color to fragment shader
    fragTexCoord = vertexTexCoord;
    fragColor = vertexColor;
    
    // Calculate screen position for border calculations
    screenPos = vec2(vertexPosition.x * screenWidth, vertexPosition.y * screenHeight);
    
    // Transform vertex position
    gl_Position = mvp * vec4(vertexPosition, 1.0);
}
