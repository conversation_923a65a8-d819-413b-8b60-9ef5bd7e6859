using System;
using System.Threading;
using System.Threading.Tasks;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Second Player that renders MIDI notes to a texture buffer as fast as possible.
    /// Only responsibility is to draw notes to texture - no other behaviors.
    /// </summary>
    public class MIDISecondPlayer : IDisposable
    {
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private RenderTexture2D renderTexture;
        private bool disposed = false;
        private bool isRendering = false;
        private Task renderTask;
        private CancellationTokenSource cancellationTokenSource;

        // Texture dimensions
        private int textureWidth = 1920;
        private int textureHeight = 1080;

        // Note rendering properties (same as main renderer)
        private double[] x1array = new double[256];
        private double[] wdtharray = new double[256];
        private bool[] blackKeys = new bool[256];
        private double fullLeft = 0;
        private double fullWidth = 1;

        public MIDIFile File
        {
            get => file;
            set
            {
                file = value;
                if (file != null)
                {
                    StartRender();
                }
                else
                {
                    StopRender();
                }
            }
        }

        public PlayingState Time
        {
            get => time;
            set
            {
                if (time != null)
                {
                    time.TimeChanged -= OnTimeChanged;
                    time.SpeedChanged -= OnSpeedChanged;
                }
                time = value;
                if (time != null)
                {
                    time.TimeChanged += OnTimeChanged;
                    time.SpeedChanged += OnSpeedChanged;
                }
            }
        }

        public RenderTexture2D RenderTexture => renderTexture;

        public MIDISecondPlayer(Settings settings)
        {
            this.settings = settings;
            InitializeKeyLayout();
            CreateRenderTexture();
        }

        private void InitializeKeyLayout()
        {
            // Initialize piano key layout (same as RaylibScene)
            for (int i = 0; i < 256; i++)
            {
                blackKeys[i] = IsBlackKey(i);
            }

            // Calculate key positions and widths
            CalculateKeyLayout();
        }

        private bool IsBlackKey(int noteNumber)
        {
            int octaveNote = noteNumber % 12;
            return octaveNote == 1 || octaveNote == 3 || octaveNote == 6 || octaveNote == 8 || octaveNote == 10;
        }

        private void CalculateKeyLayout()
        {
            // Use same key layout calculation as RaylibScene
            double whiteKeyWidth = 1.0 / 52.0; // 52 white keys in 88-key range
            double blackKeyWidth = whiteKeyWidth * 0.6;
            
            int whiteKeyIndex = 0;
            for (int i = 0; i < 256; i++)
            {
                if (blackKeys[i])
                {
                    // Black key positioning
                    x1array[i] = (whiteKeyIndex - 0.3) * whiteKeyWidth;
                    wdtharray[i] = blackKeyWidth;
                }
                else
                {
                    // White key positioning
                    x1array[i] = whiteKeyIndex * whiteKeyWidth;
                    wdtharray[i] = whiteKeyWidth;
                    whiteKeyIndex++;
                }
            }

            fullLeft = 0;
            fullWidth = 1;
        }

        private void CreateRenderTexture()
        {
            if (renderTexture.id != 0)
            {
                Raylib.UnloadRenderTexture(renderTexture);
            }
            renderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
        }

        private void OnTimeChanged()
        {
            // Restart rendering when time changes (seek operation)
            StartRender();
        }

        private void OnSpeedChanged()
        {
            // Restart rendering when note speed changes
            StartRender();
        }

        private void StartRender()
        {
            if (disposed || file == null) return;

            StopRender();

            cancellationTokenSource = new CancellationTokenSource();
            renderTask = Task.Run(() => RenderToTexture(cancellationTokenSource.Token));
        }

        private void StopRender()
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
                cancellationTokenSource = null;
            }

            if (renderTask != null)
            {
                try
                {
                    renderTask.Wait(1000); // Wait up to 1 second
                }
                catch (AggregateException)
                {
                    // Task was cancelled, ignore
                }
                renderTask = null;
            }
        }

        private void RenderToTexture(CancellationToken cancellationToken)
        {
            if (disposed || file == null || !(file is MIDIMemoryFile memoryFile)) return;

            isRendering = true;

            try
            {
                // Begin rendering to texture
                Raylib.BeginTextureMode(renderTexture);
                Raylib.ClearBackground(new RaylibColor(0, 0, 0, 0)); // Transparent background

                // Get current time and settings
                double currentTime = time?.GetTime() ?? 0;
                double timeScale = settings?.Volatile?.Size ?? 1.0;
                double renderCutoff = currentTime + timeScale;

                // Set color events and get note colors
                memoryFile.SetColorEvents(currentTime);
                var colors = memoryFile.MidiNoteColors;
                if (colors == null) return;

                // Render notes using the same logic as RaylibScene
                RenderNotesToTexture(memoryFile, currentTime, timeScale, renderCutoff, colors, cancellationToken);

                Raylib.EndTextureMode();
            }
            catch (OperationCanceledException)
            {
                // Rendering was cancelled, this is expected
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in MIDISecondPlayer rendering: {ex.Message}");
            }
            finally
            {
                isRendering = false;
            }
        }

        private void RenderNotesToTexture(MIDIMemoryFile file, double currentTime, double timeScale, double renderCutoff, NoteCol[] colors, CancellationToken cancellationToken)
        {
            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (cancellationToken.IsCancellationRequested) return;
                if (blackKeys[k]) continue; // Skip black keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors);
            }

            // Render black key notes second (foreground layer)
            for (int k = 0; k < 256; k++)
            {
                if (cancellationToken.IsCancellationRequested) return;
                if (!blackKeys[k]) continue; // Skip white keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors);
            }
        }

        private void RenderNotesForKey(MIDIMemoryFile file, int k, double currentTime, double timeScale, double renderCutoff, NoteCol[] colors)
        {
            if (file.Notes == null || k >= file.Notes.Length)
                return;

            var notes = file.Notes[k];
            if (notes == null || notes.Length == 0)
                return;

            // Process notes for this key
            foreach (var note in notes)
            {
                if (note.end < currentTime - 0.1) // Small buffer for note ending
                    continue;
                if (note.start > renderCutoff)
                    break;

                // Render note if it's visible
                if (note.start <= renderCutoff && note.end >= currentTime)
                {
                    RenderSingleNote(k, note, currentTime, timeScale, colors);
                }
            }
        }

        private void RenderSingleNote(int keyIndex, Note note, double currentTime, double timeScale, NoteCol[] colors)
        {
            // Calculate note position and size (same logic as RaylibRenderer)
            float noteLeft = (float)(x1array[keyIndex] * textureWidth);
            float noteRight = noteLeft + (float)(wdtharray[keyIndex] * textureWidth);
            
            float noteStart = (float)((note.start - currentTime) / timeScale);
            float noteEnd = (float)((note.end - currentTime) / timeScale);
            
            // Convert to screen coordinates (flipped vertically like main renderer)
            float noteAreaHeight = textureHeight;
            float noteTop = noteAreaHeight - (noteEnd * noteAreaHeight);
            float noteBottom = noteAreaHeight - (noteStart * noteAreaHeight);

            // Ensure notes are rendered in the correct area
            if (noteTop < 0) noteTop = 0;
            if (noteBottom > noteAreaHeight) noteBottom = noteAreaHeight;
            if (noteTop >= noteBottom) return; // Skip invalid notes

            // Get note color
            var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

            // Render the note using the same visual style as RaylibRenderer
            RenderKivaMIDINote(noteLeft, noteTop, noteRight - noteLeft, noteBottom - noteTop, noteColor);
        }

        private void RenderKivaMIDINote(float left, float top, float width, float height, RaylibColor noteColor)
        {
            // Use the same rendering style as RaylibRenderer.RenderKivaMIDINote
            if (width <= 0 || height <= 0) return;

            // Calculate border thickness (same as main renderer)
            float noteBorder = 0.00091f;
            float noteBorderH = (float)Math.Round(noteBorder * textureWidth) / textureWidth * textureWidth;
            float noteBorderV = (float)Math.Round(noteBorder * textureHeight) / textureHeight * textureHeight / (textureHeight / (float)textureWidth);

            // Ensure minimum border size
            noteBorderH = Math.Max(1, noteBorderH);
            noteBorderV = Math.Max(1, noteBorderV);

            // Create shadow colors (same as main renderer)
            RaylibColor shadowColorL = new RaylibColor(
                (byte)(noteColor.r * 0.4f),
                (byte)(noteColor.g * 0.4f),
                (byte)(noteColor.b * 0.4f),
                noteColor.a
            );

            // Draw shadow background
            Raylib.DrawRectangle((int)left, (int)top, (int)width, (int)height, shadowColorL);

            // Draw bright inner area
            float borderTop = top + noteBorderV;
            float borderBottom = top + height - noteBorderV;
            float borderLeft = left + noteBorderH;
            float borderRight = left + width - noteBorderH;

            if (borderRight > borderLeft && borderBottom > borderTop)
            {
                RaylibColor brightColor = new RaylibColor(
                    (byte)Math.Min(255, noteColor.r + 25),
                    (byte)Math.Min(255, noteColor.g + 25),
                    (byte)Math.Min(255, noteColor.b + 25),
                    noteColor.a
                );

                Raylib.DrawRectangle((int)borderLeft, (int)borderTop, 
                    (int)(borderRight - borderLeft), (int)(borderBottom - borderTop), brightColor);
            }
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        public void Dispose()
        {
            if (disposed) return;
            disposed = true;

            StopRender();

            if (renderTexture.id != 0)
            {
                Raylib.UnloadRenderTexture(renderTexture);
            }

            if (time != null)
            {
                time.TimeChanged -= OnTimeChanged;
                time.SpeedChanged -= OnSpeedChanged;
            }
        }
    }
}
