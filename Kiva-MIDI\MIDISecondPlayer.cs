using System;
using System.Threading;
using System.Threading.Tasks;

namespace Kiva_MIDI
{
    /// <summary>
    /// Second Player dedicated to fast pre-rendering of MIDI notes to texture
    /// This player operates independently from the main audio player and only handles note visualization
    /// </summary>
    public class MIDISecondPlayer : IDisposable
    {
        private Settings settings;
        private MIDITextureRenderer textureRenderer;
        private PlayingState time;
        private MIDIFile file;
        private bool disposed = false;
        
        // Threading
        private Task renderingTask;
        private CancellationTokenSource cancellationTokenSource;
        private readonly object lockObject = new object();
        
        // Rendering parameters
        private double currentRenderTime = 0;
        private double noteSpeed = 1.0;
        private double renderBufferSeconds = 30.0; // How much to pre-render ahead
        private bool needsRestart = false;

        public MIDITextureRenderer TextureRenderer => textureRenderer;
        public bool IsRendering => renderingTask != null && !renderingTask.IsCompleted;
        public double CurrentRenderTime => currentRenderTime;

        public MIDIFile File
        {
            get => file;
            set
            {
                lock (lockObject)
                {
                    file = value;
                    if (file != null)
                    {
                        RestartRendering();
                    }
                    else
                    {
                        StopRendering();
                    }
                }
            }
        }

        public PlayingState Time
        {
            get => time;
            set
            {
                if (time != null)
                {
                    time.TimeChanged -= OnTimeChanged;
                    time.SpeedChanged -= OnSpeedChanged;
                }
                
                time = value;
                
                if (time != null)
                {
                    time.TimeChanged += OnTimeChanged;
                    time.SpeedChanged += OnSpeedChanged;
                }
            }
        }

        public double NoteSpeed
        {
            get => noteSpeed;
            set
            {
                if (Math.Abs(noteSpeed - value) > 0.001)
                {
                    noteSpeed = value;
                    RestartRendering();
                }
            }
        }

        public MIDISecondPlayer(Settings settings, int textureWidth, int textureHeight)
        {
            this.settings = settings;
            this.textureRenderer = new MIDITextureRenderer(settings, textureWidth, textureHeight);
        }

        private void OnTimeChanged()
        {
            // User seeked to a new position - restart rendering from new position
            RestartRendering();
        }

        private void OnSpeedChanged()
        {
            // Note speed changed - restart rendering with new speed
            RestartRendering();
        }

        private void RestartRendering()
        {
            lock (lockObject)
            {
                needsRestart = true;
                StopRendering();
                
                if (file != null && time != null)
                {
                    StartRendering();
                }
            }
        }

        private void StartRendering()
        {
            if (disposed || file == null || time == null)
                return;

            cancellationTokenSource = new CancellationTokenSource();
            currentRenderTime = time.GetTime();
            
            renderingTask = Task.Run(() => RenderingLoop(cancellationTokenSource.Token));
        }

        private void StopRendering()
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
                cancellationTokenSource = null;
            }

            if (renderingTask != null)
            {
                try
                {
                    renderingTask.Wait(1000); // Wait up to 1 second for graceful shutdown
                }
                catch (AggregateException)
                {
                    // Task was cancelled, which is expected
                }
                renderingTask = null;
            }
        }

        private void RenderingLoop(CancellationToken cancellationToken)
        {
            try
            {
                var memoryFile = file as MIDIMemoryFile;
                if (memoryFile == null)
                    return;

                double startTime = currentRenderTime;
                double endTime = startTime + renderBufferSeconds;
                
                // Clear the texture and start fresh
                textureRenderer.BeginTextureRender();
                
                // Render all notes in the time range as fast as possible
                RenderNotesInTimeRange(memoryFile, startTime, endTime, cancellationToken);
                
                textureRenderer.EndTextureRender();
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in MIDISecondPlayer rendering loop: {ex.Message}");
            }
        }

        private void RenderNotesInTimeRange(MIDIMemoryFile memoryFile, double startTime, double endTime, CancellationToken cancellationToken)
        {
            if (memoryFile.Notes == null)
                return;

            // Set color events for the start time
            memoryFile.SetColorEvents(startTime);
            var colors = memoryFile.MidiNoteColors;
            
            if (colors == null)
                return;

            // Render notes for each key
            for (int k = 0; k < Math.Min(256, memoryFile.Notes.Length); k++)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var notes = memoryFile.Notes[k];
                if (notes == null || notes.Length == 0)
                    continue;

                RenderNotesForKey(k, notes, colors, startTime, endTime);
            }
        }

        private void RenderNotesForKey(int keyIndex, Note[] notes, NoteCol[] colors, double startTime, double endTime)
        {
            foreach (var note in notes)
            {
                // Only render notes that are visible in our time range
                if (note.end < startTime || note.start > endTime)
                    continue;

                // Calculate normalized time positions (0.0 to 1.0)
                // Note: We flip the coordinates so notes move upward (start at bottom, end at top)
                float noteStart = (float)((note.start - startTime) / (endTime - startTime));
                float noteEnd = (float)((note.end - startTime) / (endTime - startTime));
                
                // Clamp to texture bounds
                noteStart = Math.Max(0f, Math.Min(1f, noteStart));
                noteEnd = Math.Max(0f, Math.Min(1f, noteEnd));
                
                if (noteStart >= noteEnd)
                    continue;

                // Get note color
                var noteColor = ColorFromNoteCol(colors[note.colorPointer]);
                
                // Add note to texture renderer
                textureRenderer.AddNoteByKey(keyIndex, noteStart, noteEnd, noteColor, noteColor);
            }
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        public void Dispose()
        {
            if (!disposed)
            {
                StopRendering();
                
                if (time != null)
                {
                    time.TimeChanged -= OnTimeChanged;
                    time.SpeedChanged -= OnSpeedChanged;
                }
                
                textureRenderer?.Dispose();
                disposed = true;
            }
        }
    }
}
