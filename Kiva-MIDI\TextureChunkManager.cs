using System;
using System.Collections.Generic;
using System.Linq;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Manages 10-second chunks of MIDI notes pre-rendered to textures for smooth scrolling visualization
    /// </summary>
    public class TextureChunkManager : IDisposable
    {
        public const double CHUNK_DURATION = 10.0; // 10 seconds per chunk
        private const int MAX_CACHED_CHUNKS = 5; // Keep 5 chunks in memory (current + 2 before + 2 after)
        
        private readonly Dictionary<int, TextureChunk> cachedChunks = new Dictionary<int, TextureChunk>();
        private readonly RaylibRenderer renderer;
        private readonly Settings settings;
        private MIDIMemoryFile midiFile;
        private int textureWidth;
        private int textureHeight;
        private bool disposed = false;

        public class TextureChunk
        {
            public int ChunkIndex { get; set; }
            public double StartTime { get; set; }
            public double EndTime { get; set; }
            public RenderTexture2D RenderTexture { get; set; }
            public bool IsRendered { get; set; }
            public DateTime LastAccessed { get; set; }

            public TextureChunk(int chunkIndex, double startTime, double endTime)
            {
                ChunkIndex = chunkIndex;
                StartTime = startTime;
                EndTime = endTime;
                IsRendered = false;
                LastAccessed = DateTime.UtcNow;
            }
        }

        public TextureChunkManager(RaylibRenderer renderer, Settings settings, int textureWidth, int textureHeight)
        {
            this.renderer = renderer ?? throw new ArgumentNullException(nameof(renderer));
            this.settings = settings ?? throw new ArgumentNullException(nameof(settings));
            this.textureWidth = textureWidth;
            this.textureHeight = textureHeight;
        }

        public void SetMIDIFile(MIDIMemoryFile file)
        {
            // Clear existing chunks when switching files
            ClearAllChunks();
            midiFile = file;
        }

        public void UpdateTextureSize(int width, int height)
        {
            if (textureWidth != width || textureHeight != height)
            {
                textureWidth = width;
                textureHeight = height;
                
                // Invalidate all existing chunks since they're the wrong size
                ClearAllChunks();
            }
        }

        /// <summary>
        /// Gets the chunk index for a given time
        /// </summary>
        public int GetChunkIndex(double time)
        {
            return (int)Math.Floor(time / CHUNK_DURATION);
        }

        /// <summary>
        /// Gets the start time for a chunk index
        /// </summary>
        public double GetChunkStartTime(int chunkIndex)
        {
            return chunkIndex * CHUNK_DURATION;
        }

        /// <summary>
        /// Gets the end time for a chunk index
        /// </summary>
        public double GetChunkEndTime(int chunkIndex)
        {
            return (chunkIndex + 1) * CHUNK_DURATION;
        }

        /// <summary>
        /// Ensures the required chunks are loaded and rendered for the given time range
        /// </summary>
        public void EnsureChunksForTimeRange(double currentTime, double timeScale)
        {
            if (midiFile == null) return;

            // Calculate which chunks we need
            int currentChunk = GetChunkIndex(currentTime);
            int startChunk = Math.Max(0, currentChunk - 1); // One chunk before current
            int endChunk = Math.Min(GetChunkIndex(midiFile.MidiLength), currentChunk + 3); // Three chunks after current

            // Load and render required chunks
            for (int chunkIndex = startChunk; chunkIndex <= endChunk; chunkIndex++)
            {
                EnsureChunkLoaded(chunkIndex);
            }

            // Clean up old chunks to manage memory
            CleanupOldChunks(currentChunk);
        }

        /// <summary>
        /// Ensures a specific chunk is loaded and rendered
        /// </summary>
        private void EnsureChunkLoaded(int chunkIndex)
        {
            if (cachedChunks.TryGetValue(chunkIndex, out var chunk))
            {
                chunk.LastAccessed = DateTime.UtcNow;
                if (chunk.IsRendered) return; // Already rendered
            }
            else
            {
                // Create new chunk
                double startTime = GetChunkStartTime(chunkIndex);
                double endTime = GetChunkEndTime(chunkIndex);
                chunk = new TextureChunk(chunkIndex, startTime, endTime);
                cachedChunks[chunkIndex] = chunk;
            }

            // Render the chunk
            RenderChunk(chunk);
        }

        /// <summary>
        /// Renders a chunk to its texture
        /// </summary>
        private void RenderChunk(TextureChunk chunk)
        {
            if (midiFile == null || disposed) return;

            try
            {
                // Create render texture if needed
                if (chunk.RenderTexture.id == 0)
                {
                    chunk.RenderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
                }

                // Begin rendering to texture
                Raylib.BeginTextureMode(chunk.RenderTexture);
                Raylib.ClearBackground(RaylibColor.BLACK);

                // Set up renderer for this chunk
                renderer.BeginFrame();

                // Render MIDI notes for this time chunk
                RenderMIDINotesForChunk(chunk);

                // End rendering
                renderer.EndFrame();
                Raylib.EndTextureMode();

                chunk.IsRendered = true;
                chunk.LastAccessed = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering chunk {chunk.ChunkIndex}: {ex.Message}");
            }
        }

        /// <summary>
        /// Renders MIDI notes for a specific chunk
        /// </summary>
        private void RenderMIDINotesForChunk(TextureChunk chunk)
        {
            if (midiFile?.Notes == null) return;

            try
            {
                // Set color events for the chunk start time
                midiFile.SetColorEvents(chunk.StartTime);
                var colors = midiFile.MidiNoteColors;
                if (colors == null) return;

                // Calculate time scale for the chunk (full chunk height = CHUNK_DURATION)
                double timeScale = CHUNK_DURATION;

                // Render notes for each key
                for (int k = 0; k < Math.Min(256, midiFile.Notes.Length); k++)
                {
                    var notes = midiFile.Notes[k];
                    if (notes == null || notes.Length == 0) continue;

                    RenderNotesForKeyInChunk(notes, k, chunk, colors, timeScale);
                }

                // Render the accumulated notes
                renderer.RenderNotes();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering MIDI notes for chunk {chunk.ChunkIndex}: {ex.Message}");
            }
        }

        /// <summary>
        /// Renders notes for a specific key within a chunk
        /// </summary>
        private void RenderNotesForKeyInChunk(Note[] notes, int keyIndex, TextureChunk chunk, NoteCol[] colors, double timeScale)
        {
            foreach (var note in notes)
            {
                // Skip notes that don't overlap with this chunk
                if (note.end < chunk.StartTime || note.start > chunk.EndTime)
                    continue;

                // Calculate note position within the chunk (0.0 = top, 1.0 = bottom)
                float noteStart = (float)((note.start - chunk.StartTime) / timeScale);
                float noteEnd = (float)((note.end - chunk.StartTime) / timeScale);

                // Clamp to chunk boundaries
                noteStart = Math.Max(0f, Math.Min(1f, noteStart));
                noteEnd = Math.Max(0f, Math.Min(1f, noteEnd));

                // Skip invalid notes
                if (noteStart >= noteEnd) continue;

                // Get note color
                var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                // Add note to renderer
                renderer.AddNoteByKey(keyIndex, noteStart, noteEnd, noteColor, noteColor);
            }
        }

        /// <summary>
        /// Converts NoteCol to RaylibColor
        /// </summary>
        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        /// <summary>
        /// Gets the texture for a specific chunk
        /// </summary>
        public TextureChunk GetChunk(int chunkIndex)
        {
            cachedChunks.TryGetValue(chunkIndex, out var chunk);
            if (chunk != null)
            {
                chunk.LastAccessed = DateTime.UtcNow;
            }
            return chunk;
        }

        /// <summary>
        /// Cleans up old chunks to manage memory usage
        /// </summary>
        private void CleanupOldChunks(int currentChunk)
        {
            if (cachedChunks.Count <= MAX_CACHED_CHUNKS) return;

            // Find chunks to remove (keep current chunk and nearby chunks)
            var chunksToRemove = cachedChunks.Values
                .Where(chunk => Math.Abs(chunk.ChunkIndex - currentChunk) > 2)
                .OrderBy(chunk => chunk.LastAccessed)
                .Take(cachedChunks.Count - MAX_CACHED_CHUNKS)
                .ToList();

            foreach (var chunk in chunksToRemove)
            {
                if (chunk.RenderTexture.id != 0)
                {
                    Raylib.UnloadRenderTexture(chunk.RenderTexture);
                }
                cachedChunks.Remove(chunk.ChunkIndex);
            }
        }

        /// <summary>
        /// Clears all cached chunks
        /// </summary>
        private void ClearAllChunks()
        {
            foreach (var chunk in cachedChunks.Values)
            {
                if (chunk.RenderTexture.id != 0)
                {
                    Raylib.UnloadRenderTexture(chunk.RenderTexture);
                }
            }
            cachedChunks.Clear();
        }

        /// <summary>
        /// Handles seeking to a new time position
        /// </summary>
        public void OnSeek(double newTime)
        {
            // When seeking, we might need to load different chunks
            // The EnsureChunksForTimeRange method will handle this automatically
            // but we can optimize by pre-loading chunks around the seek position
            if (midiFile == null) return;

            int targetChunk = GetChunkIndex(newTime);

            // Pre-load chunks around the seek position
            for (int i = -1; i <= 2; i++)
            {
                int chunkIndex = targetChunk + i;
                if (chunkIndex >= 0 && chunkIndex <= GetChunkIndex(midiFile.MidiLength))
                {
                    EnsureChunkLoaded(chunkIndex);
                }
            }
        }

        /// <summary>
        /// Handles speed changes - may need to invalidate chunks if speed affects rendering
        /// </summary>
        public void OnSpeedChange(double newSpeed)
        {
            // For now, speed changes don't require chunk invalidation
            // since chunks are rendered at full resolution and scaled during display
            // This could be optimized in the future to render chunks at different scales
        }

        /// <summary>
        /// Forces re-rendering of all chunks (useful when settings change)
        /// </summary>
        public void InvalidateAllChunks()
        {
            foreach (var chunk in cachedChunks.Values)
            {
                chunk.IsRendered = false;
            }
        }

        /// <summary>
        /// Gets the total number of chunks for the current MIDI file
        /// </summary>
        public int GetTotalChunks()
        {
            if (midiFile == null) return 0;
            return GetChunkIndex(midiFile.MidiLength) + 1;
        }

        /// <summary>
        /// Gets information about chunk loading status for debugging
        /// </summary>
        public string GetChunkStatus()
        {
            if (midiFile == null) return "No MIDI file loaded";

            int totalChunks = GetTotalChunks();
            int loadedChunks = cachedChunks.Count;
            int renderedChunks = cachedChunks.Values.Count(c => c.IsRendered);

            return $"Chunks: {renderedChunks}/{loadedChunks}/{totalChunks} (rendered/loaded/total)";
        }

        public void Dispose()
        {
            if (!disposed)
            {
                ClearAllChunks();
                disposed = true;
            }
        }
    }
}
