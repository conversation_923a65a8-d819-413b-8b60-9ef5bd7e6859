using System;
using System.Drawing;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Raylib-based scene to replace DirectX scene
    /// </summary>
    public class RaylibScene : IDisposable
    {
        private RaylibRenderer renderer;
        private RaylibMIDIRenderer midiRenderer;
        private Settings settings;
        private bool isInitialized = false;
        private int windowWidth = 800;
        private int windowHeight = 600;

        public Settings Settings 
        { 
            get => settings; 
            set => settings = value; 
        }

        public MIDIFile File
        {
            get => midiRenderer?.File;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.File = value;
            }
        }

        public PlayingState Time
        {
            get => midiRenderer?.Time;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.Time = value;
            }
        }

        public long LastRenderedNoteCount => midiRenderer?.LastRenderedNoteCount ?? 0;
        public long LastNPS => midiRenderer?.LastNPS ?? 0;
        public long LastPolyphony => midiRenderer?.LastPolyphony ?? 0;
        public long NotesPassedSum => midiRenderer?.NotesPassedSum ?? 0;

        // Public access to renderer for settings updates
        public RaylibRenderer Renderer => renderer;
        public RaylibMIDIRenderer MidiRenderer => midiRenderer;

        public RaylibScene(Settings settings)
        {
            this.settings = settings;
        }

        public void Initialize(int width, int height, string title)
        {
            if (isInitialized)
                return;

            windowWidth = width;
            windowHeight = height;

            Console.WriteLine("Initializing raylib window...");
            Raylib.SetConfigFlags(ConfigFlags.FLAG_WINDOW_RESIZABLE);
            Raylib.InitWindow(width, height, title);
            Raylib.SetTargetFPS(settings.General.FPSLock);

            Console.WriteLine("Creating renderer...");
            renderer = new RaylibRenderer(settings);
            renderer.SetScreenSize(width, height);

            Console.WriteLine("Creating MIDI renderer...");
            midiRenderer = new RaylibMIDIRenderer(renderer, settings);

            isInitialized = true;
            Console.WriteLine("Raylib initialization complete.");
        }

        public void RenderMIDIContent()
        {
            if (!isInitialized)
                return;

            // Handle window resize
            int currentWidth = Raylib.GetScreenWidth();
            int currentHeight = Raylib.GetScreenHeight();
            if (currentWidth != windowWidth || currentHeight != windowHeight)
            {
                windowWidth = currentWidth;
                windowHeight = currentHeight;
                renderer?.SetScreenSize(windowWidth, windowHeight);

                // Update texture size for chunk manager
                midiRenderer?.UpdateTextureSize(windowWidth, windowHeight);
            }

            try
            {
                // Render MIDI content only (no BeginDrawing/EndDrawing here)
                if (renderer != null && midiRenderer != null)
                {
                    renderer.BeginFrame();
                    midiRenderer.Render();
                    renderer.EndFrame();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MIDI render error: {ex.Message}");
            }
        }

        public bool ShouldClose()
        {
            return !isInitialized || Raylib.WindowShouldClose();
        }

        public void SetScreenSize(int width, int height)
        {
            windowWidth = width;
            windowHeight = height;
            renderer?.SetScreenSize(width, height);
        }

        public void Dispose()
        {
            if (!isInitialized)
                return;

            midiRenderer?.Dispose();
            renderer?.Dispose();
            
            if (Raylib.IsWindowReady())
            {
                Raylib.CloseWindow();
            }

            isInitialized = false;
        }
    }

    /// <summary>
    /// MIDI-specific renderer using raylib
    /// </summary>
    public class RaylibMIDIRenderer : IDisposable
    {
        private RaylibRenderer renderer;
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private object fileLock = new object();

        // Chunk-based rendering
        private TextureChunkManager chunkManager;
        private bool useChunkRendering = true; // Enable chunk-based rendering by default

        // Statistics
        public long LastRenderedNoteCount { get; private set; } = 0;
        public long LastNPS { get; private set; } = 0;
        public long LastPolyphony { get; private set; } = 0;
        public long NotesPassedSum { get; private set; } = 0;

        // Keyboard layout
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        public MIDIFile File
        {
            get => file;
            set
            {
                lock (fileLock)
                {
                    file = value;
                    // Update chunk manager when file changes
                    if (chunkManager != null && file is MIDIMemoryFile memoryFile)
                    {
                        chunkManager.SetMIDIFile(memoryFile);
                    }
                }
            }
        }

        public PlayingState Time
        {
            get => time;
            set => time = value;
        }

        public RaylibMIDIRenderer(RaylibRenderer renderer, Settings settings)
        {
            this.renderer = renderer;
            this.settings = settings;
            InitializeKeyboardLayout();

            // Initialize chunk manager with default texture size (will be updated when screen size is known)
            chunkManager = new TextureChunkManager(renderer, settings, 1920, 1080);
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize black key pattern
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            // Calculate keyboard layout
            CalculateKeyPositions();
        }

        private void CalculateKeyPositions()
        {
            int firstNote = 0;
            int lastNote = 128;
            
            // Apply key range settings
            if (settings.General.KeyRange == KeyRangeTypes.Key88)
            {
                firstNote = 21;
                lastNote = 109;
            }
            else if (settings.General.KeyRange == KeyRangeTypes.Key256)
            {
                firstNote = 0;
                lastNote = 256;
            }

            // Calculate white key positions
            int whiteKeyCount = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i]) whiteKeyCount++;
            }

            double whiteKeyWidth = 1.0 / whiteKeyCount;
            int whiteKeyIndex = 0;

            fullLeft = 0;
            fullRight = 1;
            fullWidth = 1;

            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i])
                {
                    // White key
                    x1array[i] = whiteKeyIndex * whiteKeyWidth;
                    wdtharray[i] = whiteKeyWidth;
                    whiteKeyIndex++;
                }
                else
                {
                    // Black key
                    double blackKeyWidth = whiteKeyWidth * 0.6;
                    x1array[i] = (whiteKeyIndex - 0.5) * whiteKeyWidth - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
            }
        }

        public void UpdateTextureSize(int width, int height)
        {
            chunkManager?.UpdateTextureSize(width, height);
        }

        public void OnSeek(double newTime)
        {
            chunkManager?.OnSeek(newTime);
        }

        public void OnSpeedChange(double newSpeed)
        {
            chunkManager?.OnSpeedChange(newSpeed);
        }

        public void InvalidateChunks()
        {
            chunkManager?.InvalidateAllChunks();
        }

        public string GetChunkStatus()
        {
            return chunkManager?.GetChunkStatus() ?? "Chunk manager not initialized";
        }

        public void Render()
        {
            if (time == null || renderer == null)
                return;

            double currentTime = time.GetTime();
            double timeScale = settings?.Volatile?.Size ?? 1.0;

            lock (fileLock)
            {
                if (file != null && file is MIDIMemoryFile memoryFile)
                {
                    try
                    {
                        if (useChunkRendering)
                        {
                            RenderWithChunks(memoryFile, currentTime, timeScale);
                        }
                        else
                        {
                            // Fallback to original real-time rendering
                            double renderCutoff = currentTime + timeScale;
                            RenderMIDIFile(memoryFile, currentTime, timeScale, renderCutoff);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error rendering MIDI file: {ex.Message}");
                    }
                }
            }

            // Always render keyboard on top
            try
            {
                if (!useChunkRendering)
                {
                    renderer.RenderNotes(); // Only render notes if not using chunks
                }
                renderer.RenderKeyboard();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering keyboard: {ex.Message}");
            }
        }

        private void RenderMIDIFile(MIDIMemoryFile file, double currentTime, double timeScale, double renderCutoff)
        {
            if (file == null || renderer == null)
                return;

            try
            {
                file.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            var colors = file.MidiNoteColors;
            if (colors == null)
            {
                Console.WriteLine("Warning: MidiNoteColors is null");
                return;
            }

            long notesRendered = 0;
            int polyphony = 0;

            // Clear all key states first
            for (int k = 0; k < 256; k++)
            {
                renderer.UpdateKey(k, new RaylibColor(0, 0, 0, 0), new RaylibColor(0, 0, 0, 0), false, 0);
            }

            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (blackKeys[k]) continue; // Skip black keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            // Render black key notes second (foreground layer) - they will overlap white key notes
            for (int k = 0; k < 256; k++)
            {
                if (!blackKeys[k]) continue; // Skip white keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            LastRenderedNoteCount = notesRendered;
            LastPolyphony = polyphony;
        }

        private void RenderWithChunks(MIDIMemoryFile file, double currentTime, double timeScale)
        {
            if (file == null || renderer == null || chunkManager == null)
                return;

            try
            {
                // Ensure chunks are loaded for the current time range
                chunkManager.EnsureChunksForTimeRange(currentTime, timeScale);

                // Get screen dimensions
                int screenWidth = Raylib.GetScreenWidth();
                int screenHeight = Raylib.GetScreenHeight();
                int keyboardHeight = (int)(screenHeight * 0.2f); // Assume 20% for keyboard
                int noteAreaHeight = screenHeight - keyboardHeight;

                // Calculate which chunks are visible based on timeScale
                double visibleDuration = timeScale;
                int currentChunkIndex = chunkManager.GetChunkIndex(currentTime);

                // Determine how many chunks we might need to display
                int chunksNeeded = (int)Math.Ceiling(visibleDuration / TextureChunkManager.CHUNK_DURATION) + 1;

                // Calculate the exact position within the timeline
                double timelinePosition = currentTime;

                // Render chunks that contribute to the visible area
                for (int i = 0; i < chunksNeeded; i++)
                {
                    int chunkIndex = currentChunkIndex + i;
                    var chunk = chunkManager.GetChunk(chunkIndex);

                    if (chunk?.IsRendered == true)
                    {
                        RenderChunkTextureSliding(chunk, currentTime, timeScale, noteAreaHeight);
                    }
                }

                // Update statistics (simplified for chunk rendering)
                LastRenderedNoteCount = 1000; // Placeholder - could be calculated from visible chunks
                LastPolyphony = CalculateCurrentPolyphony(file, currentTime); // Calculate actual polyphony
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in chunk-based rendering: {ex.Message}");
            }
        }

        private void RenderChunkTextureSliding(TextureChunkManager.TextureChunk chunk, double currentTime, double timeScale, int noteAreaHeight)
        {
            if (chunk?.RenderTexture.id == 0) return;

            try
            {
                float textureHeight = chunk.RenderTexture.texture.height;
                float textureWidth = chunk.RenderTexture.texture.width;
                int screenWidth = Raylib.GetScreenWidth();

                // Calculate how this chunk relates to the current time
                double chunkStartTime = chunk.StartTime;
                double chunkEndTime = chunk.EndTime;

                // Calculate the visible portion of this chunk
                double visibleStart = Math.Max(currentTime, chunkStartTime);
                double visibleEnd = Math.Min(currentTime + timeScale, chunkEndTime);

                // Skip if chunk is not visible
                if (visibleStart >= visibleEnd) return;

                // Calculate texture coordinates
                double chunkProgress = (visibleStart - chunkStartTime) / TextureChunkManager.CHUNK_DURATION;
                double visibleDuration = visibleEnd - visibleStart;
                double visibleRatio = visibleDuration / TextureChunkManager.CHUNK_DURATION;

                // Source rectangle (what part of the texture to draw)
                float sourceY = (float)(chunkProgress * textureHeight);
                float sourceHeight = (float)(visibleRatio * textureHeight);
                var sourceRect = new Rectangle(0, sourceY, textureWidth, sourceHeight);

                // Destination rectangle (where to draw on screen)
                // Calculate screen position based on time offset
                double timeOffset = visibleStart - currentTime;
                float screenY = (float)(timeOffset / timeScale * noteAreaHeight);
                float screenHeight = (float)(visibleDuration / timeScale * noteAreaHeight);

                var destRect = new Rectangle(0, screenY, screenWidth, screenHeight);

                // Draw the texture portion
                Raylib.DrawTexturePro(
                    chunk.RenderTexture.texture,
                    sourceRect,
                    destRect,
                    new Vector2(0, 0),
                    0f,
                    RaylibColor.WHITE
                );
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering sliding chunk texture: {ex.Message}");
            }
        }

        private int CalculateCurrentPolyphony(MIDIMemoryFile file, double currentTime)
        {
            if (file?.Notes == null) return 0;

            int polyphony = 0;
            try
            {
                for (int k = 0; k < Math.Min(256, file.Notes.Length); k++)
                {
                    var notes = file.Notes[k];
                    if (notes == null) continue;

                    foreach (var note in notes)
                    {
                        if (note.start <= currentTime && note.end >= currentTime)
                        {
                            polyphony++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calculating polyphony: {ex.Message}");
            }

            return polyphony;
        }

        private void RenderNotesForKey(MIDIMemoryFile file, int k, double currentTime, double timeScale, double renderCutoff, NoteCol[] colors, ref long notesRendered, ref int polyphony)
        {
            if (file.Notes == null || k >= file.Notes.Length)
                return;

            var notes = file.Notes[k];
            if (notes == null || notes.Length == 0)
                return;

            // Use original piano layout positioning
            float left = (float)((x1array[k] - fullLeft) / fullWidth);
            float right = (float)((x1array[k] + wdtharray[k] - fullLeft) / fullWidth);
            bool pressed = false;
            var keyColor = new RaylibColor(0, 0, 0, 0);

            // Process notes for this key
            foreach (var note in notes)
            {
                if (note.end < currentTime - 0.1) // Small buffer for note ending
                    continue;
                if (note.start > renderCutoff)
                    break;

                // Check if key is currently pressed
                if (note.start <= currentTime && note.end >= currentTime)
                {
                    pressed = true;
                    polyphony++;

                    // Get note color
                    var noteCol = colors[note.colorPointer];
                    keyColor = ColorFromNoteCol(noteCol);
                }

                // Add note to render buffer if it's visible
                if (note.start <= renderCutoff && note.end >= currentTime)
                {
                    float noteStart = (float)((note.start - currentTime) / timeScale);
                    float noteEnd = (float)((note.end - currentTime) / timeScale);
                    var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                    // Use AddNoteByKey to get proper piano key widths
                    renderer.AddNoteByKey(k, noteStart, noteEnd, noteColor, noteColor);
                    notesRendered++;
                }
            }

            // Update key state
            renderer.UpdateKey(k, keyColor, keyColor, pressed, 0);
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        private bool IsBlackNote(int noteNumber)
        {
            int n = noteNumber % 12;
            return n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
        }

        public void Dispose()
        {
            chunkManager?.Dispose();
        }
    }
}
