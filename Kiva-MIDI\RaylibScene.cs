using System;
using System.Drawing;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Raylib-based scene to replace DirectX scene
    /// </summary>
    public class RaylibScene : IDisposable
    {
        private RaylibRenderer renderer;
        private RaylibMIDIRenderer midiRenderer;
        private Settings settings;
        private bool isInitialized = false;
        private int windowWidth = 800;
        private int windowHeight = 600;

        public Settings Settings 
        { 
            get => settings; 
            set => settings = value; 
        }

        public MIDIFile File
        {
            get => midiRenderer?.File;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.File = value;
            }
        }

        public PlayingState Time
        {
            get => midiRenderer?.Time;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.Time = value;
            }
        }

        public long LastRenderedNoteCount => midiRenderer?.LastRenderedNoteCount ?? 0;
        public long LastNPS => midiRenderer?.LastNPS ?? 0;
        public long LastPolyphony => midiRenderer?.LastPolyphony ?? 0;
        public long NotesPassedSum => midiRenderer?.NotesPassedSum ?? 0;

        // Public access to renderer for settings updates
        public RaylibRenderer Renderer => renderer;

        public RaylibScene(Settings settings)
        {
            this.settings = settings;
        }

        public void Initialize(int width, int height, string title)
        {
            if (isInitialized)
                return;

            windowWidth = width;
            windowHeight = height;

            Console.WriteLine("Initializing raylib window...");
            Raylib.SetConfigFlags(ConfigFlags.FLAG_WINDOW_RESIZABLE);
            Raylib.InitWindow(width, height, title);
            Raylib.SetTargetFPS(settings.General.FPSLock);

            Console.WriteLine("Creating renderer...");
            renderer = new RaylibRenderer(settings);
            renderer.SetScreenSize(width, height);

            Console.WriteLine("Creating MIDI renderer...");
            midiRenderer = new RaylibMIDIRenderer(renderer, settings);

            isInitialized = true;
            Console.WriteLine("Raylib initialization complete.");
        }

        public void RenderMIDIContent()
        {
            if (!isInitialized)
                return;

            // Handle window resize
            int currentWidth = Raylib.GetScreenWidth();
            int currentHeight = Raylib.GetScreenHeight();
            if (currentWidth != windowWidth || currentHeight != windowHeight)
            {
                windowWidth = currentWidth;
                windowHeight = currentHeight;
                renderer?.SetScreenSize(windowWidth, windowHeight);
            }

            try
            {
                // Render MIDI content only (no BeginDrawing/EndDrawing here)
                if (renderer != null && midiRenderer != null)
                {
                    renderer.BeginFrame();
                    midiRenderer.Render();
                    renderer.EndFrame();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MIDI render error: {ex.Message}");
            }
        }

        public bool ShouldClose()
        {
            return !isInitialized || Raylib.WindowShouldClose();
        }

        public void SetScreenSize(int width, int height)
        {
            windowWidth = width;
            windowHeight = height;
            renderer?.SetScreenSize(width, height);
        }

        public void Dispose()
        {
            if (!isInitialized)
                return;

            midiRenderer?.Dispose();
            renderer?.Dispose();
            
            if (Raylib.IsWindowReady())
            {
                Raylib.CloseWindow();
            }

            isInitialized = false;
        }
    }

    /// <summary>
    /// MIDI-specific renderer using raylib
    /// </summary>
    public class RaylibMIDIRenderer : IDisposable
    {
        private RaylibRenderer renderer;
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private object fileLock = new object();

        // Statistics
        public long LastRenderedNoteCount { get; private set; } = 0;
        public long LastNPS { get; private set; } = 0;
        public long LastPolyphony { get; private set; } = 0;
        public long NotesPassedSum { get; private set; } = 0;

        // Keyboard layout
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        public MIDIFile File
        {
            get => file;
            set
            {
                lock (fileLock)
                {
                    file = value;
                }
            }
        }

        public PlayingState Time
        {
            get => time;
            set => time = value;
        }

        public RaylibMIDIRenderer(RaylibRenderer renderer, Settings settings)
        {
            this.renderer = renderer;
            this.settings = settings;
            InitializeKeyboardLayout();
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize black key pattern
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            // Calculate keyboard layout
            CalculateKeyPositions();
        }

        private void CalculateKeyPositions()
        {
            int firstNote = 0;
            int lastNote = 128;
            
            // Apply key range settings
            if (settings.General.KeyRange == KeyRangeTypes.Key88)
            {
                firstNote = 21;
                lastNote = 109;
            }
            else if (settings.General.KeyRange == KeyRangeTypes.Key256)
            {
                firstNote = 0;
                lastNote = 256;
            }

            // Calculate white key positions
            int whiteKeyCount = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i]) whiteKeyCount++;
            }

            double whiteKeyWidth = 1.0 / whiteKeyCount;
            int whiteKeyIndex = 0;

            fullLeft = 0;
            fullRight = 1;
            fullWidth = 1;

            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i])
                {
                    // White key
                    x1array[i] = whiteKeyIndex * whiteKeyWidth;
                    wdtharray[i] = whiteKeyWidth;
                    whiteKeyIndex++;
                }
                else
                {
                    // Black key
                    double blackKeyWidth = whiteKeyWidth * 0.6;
                    x1array[i] = (whiteKeyIndex - 0.5) * whiteKeyWidth - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
            }
        }

        public void Render()
        {
            if (time == null || renderer == null)
                return;

            double currentTime = time.GetTime();
            double timeScale = settings?.Volatile?.Size ?? 1.0;
            double renderCutoff = currentTime + 10.0; // 10 second buffer ahead

            lock (fileLock)
            {
                if (file != null && file is MIDIMemoryFile memoryFile)
                {
                    try
                    {
                        RenderMIDIFileToScrollingTexture(memoryFile, currentTime, timeScale, renderCutoff);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error rendering MIDI file: {ex.Message}");
                    }
                }
            }

            // Update scrolling and render notes, then keyboard on top
            try
            {
                renderer.UpdateScrolling(currentTime, timeScale);
                renderer.RenderNotes();
                renderer.RenderKeyboard();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering keyboard/notes: {ex.Message}");
            }
        }

        private void RenderMIDIFileToScrollingTexture(MIDIMemoryFile file, double currentTime, double timeScale, double renderCutoff)
        {
            if (file == null || renderer == null)
                return;

            try
            {
                file.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            var colors = file.MidiNoteColors;
            if (colors == null)
            {
                Console.WriteLine("Warning: MidiNoteColors is null");
                return;
            }

            long notesRendered = 0;
            int polyphony = 0;

            // Clear the scrolling texture first to prevent recursive rendering
            renderer.ClearScrollingTexture();

            // Clear all key states first
            for (int k = 0; k < 256; k++)
            {
                renderer.UpdateKey(k, new RaylibColor(0, 0, 0, 0), new RaylibColor(0, 0, 0, 0), false, 0);
            }

            // Render notes to scrolling texture for all keys
            for (int k = 0; k < 256; k++)
            {
                RenderNotesForKeyToTexture(file, k, currentTime, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            LastRenderedNoteCount = notesRendered;
            LastPolyphony = polyphony;
        }

        private void RenderMIDIFile(MIDIMemoryFile file, double currentTime, double timeScale, double renderCutoff)
        {
            if (file == null || renderer == null)
                return;

            try
            {
                file.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            var colors = file.MidiNoteColors;
            if (colors == null)
            {
                Console.WriteLine("Warning: MidiNoteColors is null");
                return;
            }

            long notesRendered = 0;
            int polyphony = 0;

            // Clear all key states first
            for (int k = 0; k < 256; k++)
            {
                renderer.UpdateKey(k, new RaylibColor(0, 0, 0, 0), new RaylibColor(0, 0, 0, 0), false, 0);
            }

            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (blackKeys[k]) continue; // Skip black keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            // Render black key notes second (foreground layer) - they will overlap white key notes
            for (int k = 0; k < 256; k++)
            {
                if (!blackKeys[k]) continue; // Skip white keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            LastRenderedNoteCount = notesRendered;
            LastPolyphony = polyphony;
        }

        private void RenderNotesForKeyToTexture(MIDIMemoryFile file, int k, double currentTime, double renderCutoff, NoteCol[] colors, ref long notesRendered, ref int polyphony)
        {
            if (file.Notes == null || k >= file.Notes.Length)
                return;

            var notes = file.Notes[k];
            if (notes == null || notes.Length == 0)
                return;

            bool pressed = false;
            var keyColor = new RaylibColor(0, 0, 0, 0);

            // Process notes for this key
            foreach (var note in notes)
            {
                // Check if key is currently pressed
                if (note.start <= currentTime && note.end >= currentTime)
                {
                    pressed = true;
                    polyphony++;

                    // Get note color
                    var noteCol = colors[note.colorPointer];
                    keyColor = ColorFromNoteCol(noteCol);
                }

                // Add note to scrolling texture if it's within buffer range
                if (note.start <= renderCutoff && note.end >= currentTime - 0.1)
                {
                    var noteColor = ColorFromNoteCol(colors[note.colorPointer]);
                    renderer.AddNoteToScrollingTexture(k, note.start, note.end, currentTime, noteColor, noteColor);
                    notesRendered++;
                }
            }

            // Update key state
            renderer.UpdateKey(k, keyColor, keyColor, pressed, 0);
        }

        private void RenderNotesForKey(MIDIMemoryFile file, int k, double currentTime, double timeScale, double renderCutoff, NoteCol[] colors, ref long notesRendered, ref int polyphony)
        {
            if (file.Notes == null || k >= file.Notes.Length)
                return;

            var notes = file.Notes[k];
            if (notes == null || notes.Length == 0)
                return;

            // Use original piano layout positioning
            float left = (float)((x1array[k] - fullLeft) / fullWidth);
            float right = (float)((x1array[k] + wdtharray[k] - fullLeft) / fullWidth);
            bool pressed = false;
            var keyColor = new RaylibColor(0, 0, 0, 0);

            // Process notes for this key
            foreach (var note in notes)
            {
                if (note.end < currentTime - 0.1) // Small buffer for note ending
                    continue;
                if (note.start > renderCutoff)
                    break;

                // Check if key is currently pressed
                if (note.start <= currentTime && note.end >= currentTime)
                {
                    pressed = true;
                    polyphony++;

                    // Get note color
                    var noteCol = colors[note.colorPointer];
                    keyColor = ColorFromNoteCol(noteCol);
                }

                // Add note to render buffer if it's visible
                if (note.start <= renderCutoff && note.end >= currentTime)
                {
                    float noteStart = (float)((note.start - currentTime) / timeScale);
                    float noteEnd = (float)((note.end - currentTime) / timeScale);
                    var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                    // Use AddNoteByKey to get proper piano key widths
                    renderer.AddNoteByKey(k, noteStart, noteEnd, noteColor, noteColor);
                    notesRendered++;
                }
            }

            // Update key state
            renderer.UpdateKey(k, keyColor, keyColor, pressed, 0);
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        private bool IsBlackNote(int noteNumber)
        {
            int n = noteNumber % 12;
            return n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
