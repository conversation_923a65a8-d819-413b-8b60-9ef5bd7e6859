using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Kiva_MIDI
{
    /// <summary>
    /// Second Player that only renders MIDI notes to texture as fast as possible.
    /// No audio functionality - only texture rendering.
    /// </summary>
    public class MIDITexturePlayer : IDisposable
    {
        private Settings settings;
        private RaylibRenderer renderer;
        private MIDIFile file;
        private PlayingState time;
        private double noteSpeed = 1.0;
        
        // Texture rendering state
        private bool disposed = false;
        private bool isRendering = false;
        private CancellationTokenSource renderCancellation;
        private Task renderTask;
        
        // Texture buffer parameters
        private double textureTimeRange = 10.0; // Seconds of MIDI to pre-render
        private double currentRenderTime = 0.0;
        private double pixelsPerSecond = 100.0; // How many pixels per second of MIDI time
        
        public MIDIFile File
        {
            get => file;
            set
            {
                lock (this)
                {
                    file = value;
                    if (file != null)
                    {
                        RestartTextureRendering();
                    }
                    else
                    {
                        StopTextureRendering();
                    }
                }
            }
        }
        
        public PlayingState Time
        {
            get => time;
            set
            {
                if (time != null)
                {
                    time.TimeChanged -= OnTimeChanged;
                    time.SpeedChanged -= OnSpeedChanged;
                }
                time = value;
                if (time != null)
                {
                    time.TimeChanged += OnTimeChanged;
                    time.SpeedChanged += OnSpeedChanged;
                }
            }
        }
        
        public double NoteSpeed
        {
            get => noteSpeed;
            set
            {
                if (Math.Abs(noteSpeed - value) > 0.001)
                {
                    noteSpeed = value;
                    OnNoteSpeedChanged();
                }
            }
        }
        
        public MIDITexturePlayer(Settings settings, RaylibRenderer renderer)
        {
            this.settings = settings;
            this.renderer = renderer;

            // Initialize texture rendering
            renderer.EnableTextureRendering(true);
            renderer.InitializeTexture();
        }

        public void UpdateTextureScroll()
        {
            if (time == null || !isRendering) return;

            // Calculate scroll offset based on current time and note speed
            double currentTime = time.GetTime();
            double relativeTime = currentTime - currentRenderTime;
            double effectivePixelsPerSecond = pixelsPerSecond / noteSpeed;

            float scrollOffset = (float)(relativeTime * effectivePixelsPerSecond);
            renderer.SetTextureScrollOffset(scrollOffset);
        }
        
        private void OnTimeChanged()
        {
            // Main Player seeked - restart texture buffer
            RestartTextureRendering();
        }
        
        private void OnSpeedChanged()
        {
            // Main Player speed changed - restart texture buffer
            RestartTextureRendering();
        }
        
        private void OnNoteSpeedChanged()
        {
            // Note speed changed - recalculate and re-render texture
            RestartTextureRendering();
        }
        
        private void RestartTextureRendering()
        {
            if (disposed || file == null || time == null) return;
            
            StopTextureRendering();
            StartTextureRendering();
        }
        
        private void StartTextureRendering()
        {
            if (isRendering || disposed || file == null || time == null) return;
            
            isRendering = true;
            renderCancellation = new CancellationTokenSource();
            
            renderTask = Task.Run(() => RenderTextureBuffer(renderCancellation.Token), renderCancellation.Token);
        }
        
        private void StopTextureRendering()
        {
            if (!isRendering) return;
            
            isRendering = false;
            renderCancellation?.Cancel();
            
            try
            {
                renderTask?.Wait(1000); // Wait up to 1 second
            }
            catch (AggregateException) { }
            
            renderCancellation?.Dispose();
            renderCancellation = null;
            renderTask = null;
        }
        
        private void RenderTextureBuffer(CancellationToken cancellationToken)
        {
            try
            {
                if (!(file is MIDIMemoryFile memoryFile)) return;
                
                // Clear texture before rendering
                renderer.ClearTexture();

                double startTime = time.GetTime();
                currentRenderTime = startTime; // Store for scroll calculation
                double endTime = startTime + textureTimeRange;
                
                // Calculate pixels per second based on note speed
                double effectivePixelsPerSecond = pixelsPerSecond / noteSpeed;
                
                // Render all notes in the time range
                for (int k = 0; k < 256; k++)
                {
                    if (cancellationToken.IsCancellationRequested) return;
                    
                    if (memoryFile.Notes == null || k >= memoryFile.Notes.Length)
                        continue;
                        
                    var notes = memoryFile.Notes[k];
                    if (notes == null || notes.Length == 0)
                        continue;
                        
                    RenderNotesForKey(k, notes, startTime, endTime, effectivePixelsPerSecond, cancellationToken);
                }
            }
            catch (OperationCanceledException) { }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in texture rendering: {ex.Message}");
            }
        }
        
        private void RenderNotesForKey(int keyIndex, MIDIMemoryFile.Note[] notes, double startTime, double endTime, 
            double pixelsPerSecond, CancellationToken cancellationToken)
        {
            // Get key layout information from renderer
            var keyLeft = renderer.GetKeyLeft(keyIndex);
            var keyWidth = renderer.GetKeyWidth(keyIndex);
            
            foreach (var note in notes)
            {
                if (cancellationToken.IsCancellationRequested) return;
                
                // Check if note is in our time range
                if (note.end < startTime || note.start > endTime) continue;
                
                // Calculate texture Y position based on time
                double relativeStartTime = note.start - startTime;
                double relativeEndTime = note.end - startTime;
                
                float textureY = (float)(relativeStartTime * pixelsPerSecond);
                float noteHeight = (float)((relativeEndTime - relativeStartTime) * pixelsPerSecond);
                
                // Create render note
                var renderNote = new RaylibRenderer.RaylibRenderNote
                {
                    left = keyLeft,
                    right = keyLeft + keyWidth,
                    start = 0, // Will be calculated in RenderNoteToTexture
                    end = noteHeight / renderer.GetScreenHeight(), // Normalized height
                    colorLeft = GetNoteColor(note),
                    colorRight = GetNoteColor(note)
                };
                
                // Render to texture
                renderer.RenderNoteToTexture(renderNote, textureY);
            }
        }
        
        private RaylibColor GetNoteColor(MIDIMemoryFile.Note note)
        {
            // Use the same color system as the main renderer
            if (file is MIDIMemoryFile memoryFile && memoryFile.Colors != null && 
                note.colorPointer < memoryFile.Colors.Length)
            {
                var noteCol = memoryFile.Colors[note.colorPointer];
                return new RaylibColor(
                    (byte)((noteCol.rgba >> 24) & 0xFF),
                    (byte)((noteCol.rgba >> 16) & 0xFF),
                    (byte)((noteCol.rgba >> 8) & 0xFF),
                    (byte)(noteCol.rgba & 0xFF)
                );
            }
            
            // Default color
            return new RaylibColor(255, 255, 255, 255);
        }
        
        public void Dispose()
        {
            if (disposed) return;
            disposed = true;
            
            StopTextureRendering();
            
            if (time != null)
            {
                time.TimeChanged -= OnTimeChanged;
                time.SpeedChanged -= OnSpeedChanged;
            }
        }
    }
}
