#version 330

// Fragment shader for GPU-accelerated note rendering
// Replicates the visual appearance of the original Notes.fx shader

// Input from vertex shader
in vec2 fragTexCoord;
in vec4 fragColor;
in vec2 screenPos;

// Uniforms
uniform float screenWidth;
uniform float screenHeight;
uniform float noteBorder;
uniform vec4 colorLeft;
uniform vec4 colorRight;

// Output
out vec4 finalColor;

void main()
{
    // Calculate border thickness exactly like the original shader (Notes.fx lines 51-53)
    float noteBorderH = round(noteBorder * screenWidth) / screenWidth;
    float noteBorderV = round(noteBorder * screenHeight) / screenHeight / (screenHeight / screenWidth);
    
    // Ensure minimum border size
    noteBorderH = max(1.0 / screenWidth, noteBorderH);
    noteBorderV = max(1.0 / screenHeight, noteBorderV);
    
    // Get normalized coordinates within the note rectangle
    vec2 noteCoord = fragTexCoord;

    // Determine if we're in the border area
    bool inBorderH = (noteCoord.x < noteBorderH) || (noteCoord.x > (1.0 - noteBorderH));
    bool inBorderV = (noteCoord.y < noteBorderV) || (noteCoord.y > (1.0 - noteBorderV));
    bool inBorder = inBorderH || inBorderV;

    // Interpolate between left and right colors based on X coordinate
    vec4 baseColor = mix(colorLeft, colorRight, noteCoord.x);

    vec4 color = baseColor;

    if (inBorder) {
        // Shadow/border area - darker color (matching shader lines 55-60)
        // cl.xyz *= 0.2f; cl.xyz -= 0.05f; cl.xyz = clamp(cl.xyz, 0, 1);
        color.rgb = color.rgb * 0.2 - 0.05;
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    } else {
        // Inner area - brighter color (matching shader lines 88-93)
        // For left side: cl.xyz += 0.1f, for right side: cr.xyz -= 0.3f
        if (noteCoord.x < 0.5) {
            color.rgb = color.rgb + 0.1; // Left side brighter
        } else {
            color.rgb = color.rgb - 0.3; // Right side darker
        }
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    // Apply alpha blending like the original
    color.a = color.a * color.a * color.a * color.a; // Matching shader line 45-46
    
    finalColor = color;
}
