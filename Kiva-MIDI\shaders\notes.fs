#version 330

// Input from vertex shader
in vec2 fragTexCoord;
in vec4 fragColor;
in vec2 screenPos;

// Uniforms
uniform float screenWidth;
uniform float screenHeight;
uniform float screenAspect;
uniform float keyboardHeight;

// Output
out vec4 finalColor;

void main()
{
    // Get the base color from vertex
    vec4 colorL = fragColor;
    vec4 colorR = fragColor; // For now, use same color for both sides
    
    // Calculate note border (matching original shader Notes.fx lines 51-53)
    float noteBorder = 0.00091;
    float noteBorderH = round(noteBorder * screenWidth) / screenWidth;
    float noteBorderV = round(noteBorder * screenHeight) / screenHeight / screenAspect;
    
    // Get current fragment position in normalized coordinates [0,1]
    vec2 normalizedPos = fragTexCoord;
    
    // Calculate if we're in the border area
    bool inBorderH = (normalizedPos.x < noteBorderH) || (normalizedPos.x > (1.0 - noteBorderH));
    bool inBorderV = (normalizedPos.y < noteBorderV) || (normalizedPos.y > (1.0 - noteBorderV));
    
    vec4 outputColor;
    
    if (inBorderH || inBorderV) {
        // We're in the border area - use darker shadow color (matching shader lines 55-60)
        vec4 shadowColor = colorL;
        shadowColor.rgb *= 0.2;
        shadowColor.rgb -= 0.05;
        shadowColor.rgb = clamp(shadowColor.rgb, 0.0, 1.0);
        
        // Apply alpha squared effect like original shader
        shadowColor.a *= shadowColor.a;
        shadowColor.a *= shadowColor.a;
        
        outputColor = shadowColor;
    } else {
        // We're in the inner area - use brighter color (matching shader lines 88-93)
        vec4 innerColorL = colorL;
        vec4 innerColorR = colorR;
        
        innerColorL.rgb += 0.1;
        innerColorR.rgb -= 0.3;
        innerColorL.rgb = clamp(innerColorL.rgb, 0.0, 1.0);
        innerColorR.rgb = clamp(innerColorR.rgb, 0.0, 1.0);
        
        // Interpolate between left and right colors based on horizontal position
        float t = normalizedPos.x;
        outputColor = mix(innerColorL, innerColorR, t);
    }
    
    finalColor = outputColor;
}
