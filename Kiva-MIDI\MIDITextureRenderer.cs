using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;
using static Kiva_MIDI.RaylibPInvoke;

namespace Kiva_MIDI
{
    /// <summary>
    /// Specialized renderer for pre-rendering MIDI notes to texture buffers
    /// Used exclusively by the Second Player for fast texture generation
    /// Note: This is a simplified implementation that works with the existing Raylib setup
    /// </summary>
    public class MIDITextureRenderer : IDisposable
    {
        [StructLayout(LayoutKind.Sequential)]
        public struct TextureRenderNote
        {
            public float left;      // X position (left edge)
            public float right;     // X position (right edge)
            public float start;     // Y position (start time, top of note)
            public float end;       // Y position (end time, bottom of note)
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
        }

        private Settings settings;
        private List<TextureRenderNote> noteBuffer;
        private bool disposed = false;
        private bool isRenderingToTexture = false;

        // Texture dimensions
        private int textureWidth;
        private int textureHeight;

        // Piano keyboard layout arrays (same as <PERSON>lib<PERSON><PERSON><PERSON>)
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private bool[] blackKeys = new bool[257];

        public int TextureWidth => textureWidth;
        public int TextureHeight => textureHeight;
        public bool IsRenderingToTexture => isRenderingToTexture;

        public MIDITextureRenderer(Settings settings, int textureWidth, int textureHeight)
        {
            this.settings = settings;
            this.textureWidth = textureWidth;
            this.textureHeight = textureHeight;
            this.noteBuffer = new List<TextureRenderNote>();

            InitializeKeyboardLayout();
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize keyboard layout (same logic as RaylibRenderer)
            int firstNote = 0;
            int lastNote = 127;

            // Calculate keyboard layout
            double whiteKeyWidth = (double)textureWidth / GetWhiteKeyCount(firstNote, lastNote + 1);
            double blackKeyWidth = whiteKeyWidth * 0.6;

            double currentX = 0;
            for (int i = firstNote; i <= lastNote; i++)
            {
                bool isBlack = IsBlackNote(i);
                blackKeys[i] = isBlack;

                if (isBlack)
                {
                    // Black key positioning
                    x1array[i] = currentX - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
                else
                {
                    // White key positioning
                    x1array[i] = currentX;
                    wdtharray[i] = whiteKeyWidth;
                    currentX += whiteKeyWidth;
                }
            }
        }

        private bool IsBlackNote(int noteNumber)
        {
            int noteInOctave = noteNumber % 12;
            return noteInOctave == 1 || noteInOctave == 3 || noteInOctave == 6 || 
                   noteInOctave == 8 || noteInOctave == 10;
        }

        private int GetWhiteKeyCount(int firstNote, int lastNote)
        {
            int count = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!IsBlackNote(i))
                    count++;
            }
            return count;
        }

        public void BeginTextureRender()
        {
            // Mark that we're in texture rendering mode
            isRenderingToTexture = true;
            noteBuffer.Clear();
        }

        public void EndTextureRender()
        {
            // Process all accumulated notes (for now, just store them)
            // In a full implementation, this would render to an actual texture
            // For the current implementation, the notes are available via GetRenderedNotes()
            isRenderingToTexture = false;
        }

        public void AddNoteByKey(int keyIndex, float start, float end, RaylibColor colorLeft, RaylibColor colorRight)
        {
            if (keyIndex < 0 || keyIndex >= x1array.Length)
                return;

            // Additional safety check
            if (keyIndex >= 257)
                return;

            // Use normalized coordinates (0.0 to 1.0) like the main renderer expects
            float noteLeft = (float)(x1array[keyIndex] / textureWidth);
            float noteRight = (float)((x1array[keyIndex] + wdtharray[keyIndex]) / textureWidth);

            noteBuffer.Add(new TextureRenderNote
            {
                left = noteLeft,
                right = noteRight,
                start = start,
                end = end,
                colorLeft = colorLeft,
                colorRight = colorRight
            });
        }

        /// <summary>
        /// Get the rendered notes for external use (e.g., by the main renderer)
        /// </summary>
        public List<TextureRenderNote> GetRenderedNotes()
        {
            return new List<TextureRenderNote>(noteBuffer);
        }

        /// <summary>
        /// Utility method for blending colors (used by external renderers)
        /// </summary>
        public static RaylibColor BlendColors(RaylibColor color1, RaylibColor color2, float t)
        {
            t = Math.Max(0f, Math.Min(1f, t));

            return new RaylibColor(
                (byte)(color1.r * (1 - t) + color2.r * t),
                (byte)(color1.g * (1 - t) + color2.g * t),
                (byte)(color1.b * (1 - t) + color2.b * t),
                (byte)(color1.a * (1 - t) + color2.a * t)
            );
        }

        public void Dispose()
        {
            if (!disposed)
            {
                noteBuffer?.Clear();
                disposed = true;
            }
        }
    }
}
