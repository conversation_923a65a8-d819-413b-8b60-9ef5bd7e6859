using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;
using static Kiva_MIDI.RaylibPInvoke;

namespace Kiva_MIDI
{
    /// <summary>
    /// Specialized renderer for pre-rendering MIDI notes to texture buffers
    /// Used exclusively by the Second Player for fast texture generation
    /// </summary>
    public class MIDITextureRenderer : IDisposable
    {
        [StructLayout(LayoutKind.Sequential)]
        public struct TextureRenderNote
        {
            public float left;      // X position (left edge)
            public float right;     // X position (right edge)
            public float start;     // Y position (start time, top of note)
            public float end;       // Y position (end time, bottom of note)
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
        }

        private Settings settings;
        private RenderTexture2D renderTexture;
        private List<TextureRenderNote> noteBuffer;
        private bool disposed = false;

        // Texture dimensions
        private int textureWidth;
        private int textureHeight;

        // Piano keyboard layout arrays (same as RaylibRenderer)
        private double[] x1array = new double[256];
        private double[] wdtharray = new double[256];
        private bool[] blackKeys = new bool[256];

        public Texture2D Texture => renderTexture.texture;
        public int TextureWidth => textureWidth;
        public int TextureHeight => textureHeight;

        public MIDITextureRenderer(Settings settings, int textureWidth, int textureHeight)
        {
            this.settings = settings;
            this.textureWidth = textureWidth;
            this.textureHeight = textureHeight;
            this.noteBuffer = new List<TextureRenderNote>();

            InitializeKeyboardLayout();
            CreateRenderTexture();
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize keyboard layout (same logic as RaylibRenderer)
            int firstNote = 0;
            int lastNote = 127;

            // Calculate keyboard layout
            double whiteKeyWidth = (double)textureWidth / GetWhiteKeyCount(firstNote, lastNote + 1);
            double blackKeyWidth = whiteKeyWidth * 0.6;

            double currentX = 0;
            for (int i = firstNote; i <= lastNote; i++)
            {
                bool isBlack = IsBlackNote(i);
                blackKeys[i] = isBlack;

                if (isBlack)
                {
                    // Black key positioning
                    x1array[i] = currentX - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
                else
                {
                    // White key positioning
                    x1array[i] = currentX;
                    wdtharray[i] = whiteKeyWidth;
                    currentX += whiteKeyWidth;
                }
            }
        }

        private bool IsBlackNote(int noteNumber)
        {
            int noteInOctave = noteNumber % 12;
            return noteInOctave == 1 || noteInOctave == 3 || noteInOctave == 6 || 
                   noteInOctave == 8 || noteInOctave == 10;
        }

        private int GetWhiteKeyCount(int firstNote, int lastNote)
        {
            int count = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!IsBlackNote(i))
                    count++;
            }
            return count;
        }

        private void CreateRenderTexture()
        {
            renderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
        }

        public void BeginTextureRender()
        {
            Raylib.BeginTextureMode(renderTexture);
            Raylib.ClearBackground(new RaylibColor(0, 0, 0, 0)); // Transparent background
            noteBuffer.Clear();
        }

        public void EndTextureRender()
        {
            // Render all accumulated notes
            RenderNotesToTexture();
            Raylib.EndTextureMode();
        }

        public void AddNoteByKey(int keyIndex, float start, float end, RaylibColor colorLeft, RaylibColor colorRight)
        {
            if (keyIndex < 0 || keyIndex >= x1array.Length)
                return;

            // Use the same width calculations as the keyboard layout
            float noteLeft = (float)x1array[keyIndex];
            float noteRight = noteLeft + (float)wdtharray[keyIndex];

            noteBuffer.Add(new TextureRenderNote
            {
                left = noteLeft,
                right = noteRight,
                start = start,
                end = end,
                colorLeft = colorLeft,
                colorRight = colorRight
            });
        }

        private void RenderNotesToTexture()
        {
            foreach (var note in noteBuffer)
            {
                RenderSingleNote(note);
            }
        }

        private void RenderSingleNote(TextureRenderNote note)
        {
            // Convert normalized coordinates to texture coordinates
            float noteLeft = note.left;
            float noteRight = note.right;
            float noteTop = note.start * textureHeight;
            float noteBottom = note.end * textureHeight;

            // Ensure valid dimensions
            float width = noteRight - noteLeft;
            float height = noteBottom - noteTop;

            if (width <= 0 || height <= 0)
                return;

            // Render note with gradient effect (simplified for performance)
            RenderNoteWithGradient(noteLeft, noteTop, width, height, note.colorLeft, note.colorRight);
        }

        private void RenderNoteWithGradient(float x, float y, float width, float height, 
                                          RaylibColor colorLeft, RaylibColor colorRight)
        {
            // For performance, render as solid color if note is very small
            if (width < 4 || height < 4)
            {
                var avgColor = BlendColors(colorLeft, colorRight, 0.5f);
                Raylib.DrawRectangle((int)x, (int)y, (int)width, (int)height, avgColor);
                return;
            }

            // Draw gradient by drawing vertical strips (simulating shader interpolation)
            int strips = Math.Min((int)width, 16); // Limit strips for performance
            float stripWidth = width / strips;

            for (int i = 0; i < strips; i++)
            {
                float t = strips > 1 ? (float)i / (strips - 1) : 0;
                RaylibColor blendedColor = BlendColors(colorLeft, colorRight, t);

                int stripX = (int)(x + i * stripWidth);
                int stripW = (int)Math.Ceiling(stripWidth);

                // Ensure we don't go beyond the rectangle bounds
                if (stripX + stripW > x + width)
                    stripW = (int)(x + width - stripX);

                Raylib.DrawRectangle(stripX, (int)y, stripW, (int)height, blendedColor);
            }
        }

        private RaylibColor BlendColors(RaylibColor color1, RaylibColor color2, float t)
        {
            t = Math.Max(0f, Math.Min(1f, t));
            
            return new RaylibColor(
                (byte)(color1.r * (1 - t) + color2.r * t),
                (byte)(color1.g * (1 - t) + color2.g * t),
                (byte)(color1.b * (1 - t) + color2.b * t),
                (byte)(color1.a * (1 - t) + color2.a * t)
            );
        }

        public void Dispose()
        {
            if (!disposed)
            {
                Raylib.UnloadRenderTexture(renderTexture);
                disposed = true;
            }
        }
    }
}
