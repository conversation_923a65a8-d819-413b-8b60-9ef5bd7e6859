using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using static Kiva_MIDI.RaylibPInvoke;

namespace Kiva_MIDI
{
    /// <summary>
    /// Specialized renderer for pre-rendering MIDI notes to texture buffers
    /// Used exclusively by the Second Player for fast texture generation
    /// </summary>
    public class MIDITextureRenderer : IDisposable
    {
        [StructLayout(LayoutKind.Sequential)]
        public struct TextureRenderNote
        {
            public float left;      // X position (left edge)
            public float right;     // X position (right edge)
            public float start;     // Y position (start time, top of note)
            public float end;       // Y position (end time, bottom of note)
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
        }

        private Settings settings;
        private int textureWidth;
        private int textureHeight;
        private RenderTexture2D renderTexture;
        private bool disposed = false;
        
        // Rendering data
        private List<TextureRenderNote> noteBuffer = new List<TextureRenderNote>();
        
        // Keyboard layout data (copied from RaylibRenderer for consistency)
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        public RenderTexture2D RenderTexture => renderTexture;
        public int TextureWidth => textureWidth;
        public int TextureHeight => textureHeight;

        public MIDITextureRenderer(Settings settings, int width, int height)
        {
            this.settings = settings;
            this.textureWidth = width;
            this.textureHeight = height;
            
            InitializeKeyboardLayout();
            CreateRenderTexture();
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize black key pattern (same as RaylibRenderer)
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            // Calculate keyboard layout similar to original
            int firstNote = 0;
            int lastNote = 128;

            // Determine key range based on settings
            if (settings.General.KeyRange == KeyRangeTypes.KeyDynamic ||
                settings.General.KeyRange == KeyRangeTypes.Key128)
            {
                firstNote = 0;
                lastNote = 128;
            }
            else if (settings.General.KeyRange == KeyRangeTypes.Key256)
            {
                firstNote = 0;
                lastNote = 256;
            }
            else if (settings.General.KeyRange == KeyRangeTypes.Key88)
            {
                firstNote = 21;
                lastNote = 109;
            }

            // Calculate key positions and widths
            double whiteKeyWidth = 1.0 / GetWhiteKeyCount(firstNote, lastNote);
            double blackKeyWidth = whiteKeyWidth * 0.6;
            
            fullLeft = 0;
            fullRight = 1;
            fullWidth = 1;

            double currentX = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (IsBlackNote(i))
                {
                    // Black key positioning
                    x1array[i] = currentX - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
                else
                {
                    // White key positioning
                    x1array[i] = currentX;
                    wdtharray[i] = whiteKeyWidth;
                    currentX += whiteKeyWidth;
                }
            }
        }

        private bool IsBlackNote(int note)
        {
            int noteInOctave = note % 12;
            return noteInOctave == 1 || noteInOctave == 3 || noteInOctave == 6 || 
                   noteInOctave == 8 || noteInOctave == 10;
        }

        private int GetWhiteKeyCount(int firstNote, int lastNote)
        {
            int count = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!IsBlackNote(i))
                    count++;
            }
            return count;
        }

        private void CreateRenderTexture()
        {
            renderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
        }

        public void BeginTextureRender()
        {
            Raylib.BeginTextureMode(renderTexture);
            Raylib.ClearBackground(new RaylibColor(0, 0, 0, 0)); // Transparent background
            noteBuffer.Clear();
        }

        public void EndTextureRender()
        {
            // Render all accumulated notes
            RenderNotesToTexture();
            Raylib.EndTextureMode();
        }

        public void AddNoteByKey(int keyIndex, float start, float end, RaylibColor colorLeft, RaylibColor colorRight)
        {
            if (keyIndex < 0 || keyIndex >= x1array.Length)
                return;

            // Use the same width calculations as the keyboard layout
            float noteLeft = (float)x1array[keyIndex];
            float noteRight = noteLeft + (float)wdtharray[keyIndex];

            noteBuffer.Add(new TextureRenderNote
            {
                left = noteLeft,
                right = noteRight,
                start = start,
                end = end,
                colorLeft = colorLeft,
                colorRight = colorRight
            });
        }

        private void RenderNotesToTexture()
        {
            foreach (var note in noteBuffer)
            {
                RenderSingleNote(note);
            }
        }

        private void RenderSingleNote(TextureRenderNote note)
        {
            // Convert normalized coordinates to texture coordinates
            float noteLeft = note.left * textureWidth;
            float noteRight = note.right * textureWidth;

            // Convert time coordinates to texture Y coordinates (flipped vertically)
            float noteTop = note.end * textureHeight;
            float noteBottom = note.start * textureHeight;

            // Ensure notes are rendered within texture bounds
            if (noteTop < 0) noteTop = 0;
            if (noteBottom > textureHeight) noteBottom = textureHeight;
            if (noteTop >= noteBottom) return; // Skip invalid notes

            float width = noteRight - noteLeft;
            float height = noteBottom - noteTop;

            if (width <= 0 || height <= 0) return;

            // Render note with gradient effect (simplified for performance)
            RenderNoteWithGradient(noteLeft, noteTop, width, height, note.colorLeft, note.colorRight);
        }

        private void RenderNoteWithGradient(float x, float y, float width, float height, 
                                          RaylibColor colorLeft, RaylibColor colorRight)
        {
            // For performance, render as solid color if note is very small
            if (width < 4 || height < 4)
            {
                var avgColor = BlendColors(colorLeft, colorRight, 0.5f);
                Raylib.DrawRectangle((int)x, (int)y, (int)width, (int)height, avgColor);
                return;
            }

            // Render gradient by drawing multiple vertical strips
            int strips = Math.Min(8, (int)width); // Limit strips for performance
            float stripWidth = width / strips;

            for (int i = 0; i < strips; i++)
            {
                float t = (float)i / (strips - 1);
                var stripColor = BlendColors(colorLeft, colorRight, t);
                
                float stripX = x + i * stripWidth;
                Raylib.DrawRectangle((int)stripX, (int)y, (int)Math.Ceiling(stripWidth), (int)height, stripColor);
            }
        }

        private RaylibColor BlendColors(RaylibColor color1, RaylibColor color2, float t)
        {
            return new RaylibColor(
                (byte)(color1.r * (1 - t) + color2.r * t),
                (byte)(color1.g * (1 - t) + color2.g * t),
                (byte)(color1.b * (1 - t) + color2.b * t),
                (byte)(color1.a * (1 - t) + color2.a * t)
            );
        }

        public void Dispose()
        {
            if (!disposed)
            {
                if (renderTexture.id != 0)
                {
                    Raylib.UnloadRenderTexture(renderTexture);
                }
                disposed = true;
            }
        }
    }
}
